package com.example.coloringproject.factory

import com.example.coloringproject.SimpleMainActivityRefactored
import com.example.coloringproject.databinding.ActivitySimpleMainBinding
import com.example.coloringproject.manager.*

/**
 * 管理器工厂
 * 负责创建和管理所有的管理器实例
 */
class ManagerFactory(
    private val activity: SimpleMainActivityRefactored,
    private val binding: ActivitySimpleMainBinding
) {
    
    /**
     * 创建UI状态管理器
     */
    fun createUIStateManager(): UIStateManager {
        return UIStateManager(activity, binding)
    }
    
    /**
     * 创建进度管理器
     */
    fun createProgressManager(): ProgressManager {
        return ProgressManager(activity, binding)
    }
    
    /**
     * 创建颜色管理器
     */
    fun createColorManager(): ColorManager {
        return ColorManager(activity, binding)
    }
    
    /**
     * 创建项目加载器
     */
    fun createProjectLoader(): ProjectLoader {
        return ProjectLoader(activity, binding)
    }
    
    /**
     * 创建项目设置管理器
     */
    fun createProjectSetupManager(): ProjectSetupManager {
        return ProjectSetupManager(activity, binding)
    }
    
    /**
     * 创建自动演示管理器
     */
    fun createAutoDemoManager(): AutoDemoManager {
        return AutoDemoManager(activity, binding)
    }
    
    /**
     * 创建图片保存管理器
     */
    fun createImageSaveManager(): ImageSaveManager {
        return ImageSaveManager(activity, binding)
    }
    
    /**
     * 创建所有管理器的集合
     */
    fun createAllManagers(): ManagerCollection {
        return ManagerCollection(
            uiStateManager = createUIStateManager(),
            progressManager = createProgressManager(),
            colorManager = createColorManager(),
            projectLoader = createProjectLoader(),
            projectSetupManager = createProjectSetupManager(),
            autoDemoManager = createAutoDemoManager(),
            imageSaveManager = createImageSaveManager()
        )
    }
}

/**
 * 管理器集合
 * 包含所有管理器的实例，便于统一管理
 */
data class ManagerCollection(
    val uiStateManager: UIStateManager,
    val progressManager: ProgressManager,
    val colorManager: ColorManager,
    val projectLoader: ProjectLoader,
    val projectSetupManager: ProjectSetupManager,
    val autoDemoManager: AutoDemoManager,
    val imageSaveManager: ImageSaveManager
) {
    
    /**
     * 初始化所有管理器
     */
    fun initializeAll() {
        uiStateManager.initialize()
        progressManager.initialize()
        colorManager.initialize()
        projectLoader.initialize()
        projectSetupManager.initialize()
        autoDemoManager.initialize()
        imageSaveManager.initialize()
    }
    
    /**
     * 清理所有管理器
     */
    fun cleanupAll() {
        uiStateManager.cleanup()
        progressManager.cleanup()
        colorManager.cleanup()
        projectLoader.cleanup()
        projectSetupManager.cleanup()
        autoDemoManager.cleanup()
        imageSaveManager.cleanup()
    }
    
    /**
     * 获取所有管理器的列表
     */
    fun getAllManagers(): List<BaseManager> {
        return listOf(
            uiStateManager,
            progressManager,
            colorManager,
            projectLoader,
            projectSetupManager,
            autoDemoManager,
            imageSaveManager
        )
    }
}
