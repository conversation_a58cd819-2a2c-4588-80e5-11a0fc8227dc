package com.example.coloringproject.network

import android.content.Context
import android.net.ConnectivityManager
import android.net.NetworkCapabilities
import android.util.Log
import com.example.coloringproject.config.NetworkConfig
import kotlinx.coroutines.*
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import okhttp3.OkHttpClient
import okhttp3.Request
import java.io.File
import java.io.FileOutputStream
import java.io.IOException
import java.io.InputStream
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.PriorityBlockingQueue
import java.util.concurrent.atomic.AtomicInteger
import java.util.concurrent.atomic.AtomicLong

/**
 * 并发下载管理器
 * 第三阶段性能优化：支持并发下载、优先级队列、断点续传
 */
class ConcurrentDownloadManager private constructor(private val context: Context) {
    
    companion object {
        private const val TAG = "ConcurrentDownloadManager"
        
        // 下载配置
        private const val MAX_CONCURRENT_DOWNLOADS = 3
        private const val DOWNLOAD_TIMEOUT_SECONDS = 60L
        private const val RETRY_MAX_ATTEMPTS = 3
        private const val RETRY_DELAY_MS = 1000L
        
        // 断点续传配置
        private const val RESUME_THRESHOLD_BYTES = 1024 * 1024 // 1MB以上支持断点续传
        
        @Volatile
        private var INSTANCE: ConcurrentDownloadManager? = null
        
        fun getInstance(context: Context): ConcurrentDownloadManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: ConcurrentDownloadManager(context.applicationContext).also { INSTANCE = it }
            }
        }
    }
    
    private val connectivityManager = context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
    private val downloadScope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    
    // HTTP客户端
    private val client = OkHttpClient.Builder()
        .connectTimeout(DOWNLOAD_TIMEOUT_SECONDS, java.util.concurrent.TimeUnit.SECONDS)
        .readTimeout(DOWNLOAD_TIMEOUT_SECONDS, java.util.concurrent.TimeUnit.SECONDS)
        .writeTimeout(DOWNLOAD_TIMEOUT_SECONDS, java.util.concurrent.TimeUnit.SECONDS)
        .build()
    
    // 下载队列和状态管理
    private val downloadQueue = PriorityBlockingQueue<DownloadTask>()
    private val activeDownloads = ConcurrentHashMap<String, Job>()
    private val downloadStates = ConcurrentHashMap<String, DownloadState>()
    private val downloadChannel = Channel<DownloadTask>(Channel.UNLIMITED)
    
    // 统计信息
    private val totalDownloads = AtomicInteger(0)
    private val completedDownloads = AtomicInteger(0)
    private val failedDownloads = AtomicInteger(0)
    private val totalBytesDownloaded = AtomicLong(0)
    
    // 网络状态
    private val _networkState = MutableStateFlow(NetworkState.UNKNOWN)
    val networkState: StateFlow<NetworkState> = _networkState
    
    /**
     * 下载优先级
     */
    enum class DownloadPriority(val value: Int) {
        LOW(1),
        NORMAL(2),
        HIGH(3),
        CRITICAL(4)
    }
    
    /**
     * 网络状态
     */
    enum class NetworkState {
        UNKNOWN,
        WIFI,
        CELLULAR,
        NO_CONNECTION
    }
    
    /**
     * 下载状态
     */
    data class DownloadState(
        val taskId: String,
        val url: String,
        val targetFile: File,
        val priority: DownloadPriority,
        val status: DownloadStatus,
        val progress: Float = 0f,
        val bytesDownloaded: Long = 0L,
        val totalBytes: Long = 0L,
        val speed: Long = 0L, // bytes/second
        val error: String? = null,
        val startTime: Long = 0L,
        val endTime: Long = 0L
    )
    
    /**
     * 下载状态枚举
     */
    enum class DownloadStatus {
        QUEUED,
        DOWNLOADING,
        PAUSED,
        COMPLETED,
        FAILED,
        CANCELLED
    }
    
    /**
     * 下载任务
     */
    data class DownloadTask(
        val taskId: String,
        val url: String,
        val targetFile: File,
        val priority: DownloadPriority,
        val onProgress: (Float) -> Unit = {},
        val onComplete: (Result<File>) -> Unit = {},
        val supportResume: Boolean = true,
        val retryCount: Int = 0
    ) : Comparable<DownloadTask> {
        
        override fun compareTo(other: DownloadTask): Int {
            // 优先级高的排在前面
            return other.priority.value.compareTo(this.priority.value)
        }
    }
    
    init {
        // 启动下载工作协程
        startDownloadWorkers()
        
        // 监控网络状态
        monitorNetworkState()
    }
    
    /**
     * 启动下载工作协程
     */
    private fun startDownloadWorkers() {
        Log.d(TAG, "🚀 启动 $MAX_CONCURRENT_DOWNLOADS 个下载工作协程")
        
        repeat(MAX_CONCURRENT_DOWNLOADS) { workerId ->
            downloadScope.launch {
                while (true) {
                    try {
                        val task = downloadChannel.receive()
                        processDownloadTask(workerId, task)
                    } catch (e: Exception) {
                        Log.e(TAG, "下载工作协程 $workerId 异常", e)
                        delay(1000) // 异常后短暂延迟
                    }
                }
            }
        }
        
        // 任务分发协程
        downloadScope.launch {
            while (true) {
                try {
                    val task = downloadQueue.take() // 阻塞等待任务
                    downloadChannel.send(task)
                } catch (e: Exception) {
                    Log.e(TAG, "任务分发异常", e)
                }
            }
        }
    }
    
    /**
     * 监控网络状态
     */
    private fun monitorNetworkState() {
        downloadScope.launch {
            while (true) {
                try {
                    val currentState = getCurrentNetworkState()
                    if (_networkState.value != currentState) {
                        _networkState.value = currentState
                        Log.d(TAG, "网络状态变化: $currentState")
                        handleNetworkStateChange(currentState)
                    }
                    delay(5000) // 每5秒检查一次
                } catch (e: Exception) {
                    Log.e(TAG, "网络状态监控异常", e)
                    delay(10000) // 异常时延长检查间隔
                }
            }
        }
    }
    
    /**
     * 获取当前网络状态
     */
    private fun getCurrentNetworkState(): NetworkState {
        return try {
            val network = connectivityManager.activeNetwork ?: return NetworkState.NO_CONNECTION
            val capabilities = connectivityManager.getNetworkCapabilities(network) ?: return NetworkState.NO_CONNECTION
            
            when {
                capabilities.hasTransport(NetworkCapabilities.TRANSPORT_WIFI) -> NetworkState.WIFI
                capabilities.hasTransport(NetworkCapabilities.TRANSPORT_CELLULAR) -> NetworkState.CELLULAR
                else -> NetworkState.UNKNOWN
            }
        } catch (e: Exception) {
            Log.e(TAG, "获取网络状态失败", e)
            NetworkState.UNKNOWN
        }
    }
    
    /**
     * 处理网络状态变化
     */
    private fun handleNetworkStateChange(newState: NetworkState) {
        when (newState) {
            NetworkState.NO_CONNECTION -> {
                Log.w(TAG, "网络连接丢失，暂停所有下载")
                pauseAllDownloads()
            }
            NetworkState.WIFI -> {
                Log.d(TAG, "切换到WiFi，恢复所有下载")
                resumeAllDownloads()
            }
            NetworkState.CELLULAR -> {
                Log.d(TAG, "切换到移动网络，根据设置决定是否继续下载")
                // 可以根据用户设置决定是否在移动网络下载
                if (shouldDownloadOnCellular()) {
                    resumeAllDownloads()
                } else {
                    pauseAllDownloads()
                }
            }
            NetworkState.UNKNOWN -> {
                Log.d(TAG, "网络状态未知，保持当前下载状态")
            }
        }
    }
    
    /**
     * 添加下载任务
     */
    fun addDownloadTask(
        taskId: String,
        url: String,
        targetFile: File,
        priority: DownloadPriority = DownloadPriority.NORMAL,
        onProgress: (Float) -> Unit = {},
        onComplete: (Result<File>) -> Unit = {},
        supportResume: Boolean = true
    ): String {
        
        // 检查是否已存在相同任务
        if (activeDownloads.containsKey(taskId)) {
            Log.w(TAG, "任务已存在: $taskId")
            return taskId
        }
        
        // 创建下载任务
        val task = DownloadTask(
            taskId = taskId,
            url = url,
            targetFile = targetFile,
            priority = priority,
            onProgress = onProgress,
            onComplete = onComplete,
            supportResume = supportResume
        )
        
        // 初始化下载状态
        downloadStates[taskId] = DownloadState(
            taskId = taskId,
            url = url,
            targetFile = targetFile,
            priority = priority,
            status = DownloadStatus.QUEUED
        )
        
        // 添加到队列
        downloadQueue.offer(task)
        totalDownloads.incrementAndGet()
        
        Log.d(TAG, "添加下载任务: $taskId (优先级: $priority)")
        return taskId
    }
    
    /**
     * 处理下载任务
     */
    private suspend fun processDownloadTask(workerId: Int, task: DownloadTask) = withContext(Dispatchers.IO) {
        val taskId = task.taskId
        Log.d(TAG, "工作协程 $workerId 开始处理任务: $taskId")
        
        // 检查网络状态
        if (_networkState.value == NetworkState.NO_CONNECTION) {
            Log.w(TAG, "无网络连接，任务重新入队: $taskId")
            delay(5000)
            downloadQueue.offer(task)
            return@withContext
        }
        
        // 更新状态为下载中
        updateDownloadState(taskId) { it.copy(status = DownloadStatus.DOWNLOADING, startTime = System.currentTimeMillis()) }
        
        val job = launch {
            try {
                val result = executeDownload(task)
                
                if (result.isSuccess) {
                    // 下载成功
                    updateDownloadState(taskId) { 
                        it.copy(
                            status = DownloadStatus.COMPLETED,
                            progress = 1.0f,
                            endTime = System.currentTimeMillis()
                        )
                    }
                    completedDownloads.incrementAndGet()
                    task.onComplete(result)
                    Log.d(TAG, "✅ 下载完成: $taskId")
                    
                } else {
                    // 下载失败，考虑重试
                    val exception = result.exceptionOrNull()
                    Log.w(TAG, "❌ 下载失败: $taskId - ${exception?.message}")
                    
                    if (task.retryCount < RETRY_MAX_ATTEMPTS) {
                        // 重试
                        val retryTask = task.copy(retryCount = task.retryCount + 1)
                        Log.d(TAG, "重试下载: $taskId (第${retryTask.retryCount}次)")
                        delay(RETRY_DELAY_MS * retryTask.retryCount) // 递增延迟
                        downloadQueue.offer(retryTask)
                    } else {
                        // 重试次数用完，标记为失败
                        updateDownloadState(taskId) { 
                            it.copy(
                                status = DownloadStatus.FAILED,
                                error = exception?.message ?: "下载失败",
                                endTime = System.currentTimeMillis()
                            )
                        }
                        failedDownloads.incrementAndGet()
                        task.onComplete(result)
                    }
                }
                
            } catch (e: Exception) {
                Log.e(TAG, "处理下载任务异常: $taskId", e)
                updateDownloadState(taskId) { 
                    it.copy(
                        status = DownloadStatus.FAILED,
                        error = e.message ?: "未知错误",
                        endTime = System.currentTimeMillis()
                    )
                }
                failedDownloads.incrementAndGet()
                task.onComplete(Result.failure(e))
            }
        }
        
        activeDownloads[taskId] = job
        job.join()
        activeDownloads.remove(taskId)
    }
    
    /**
     * 执行下载
     */
    private suspend fun executeDownload(task: DownloadTask): Result<File> = withContext(Dispatchers.IO) {
        try {
            val targetFile = task.targetFile
            val tempFile = File(targetFile.parentFile, "${targetFile.name}.tmp")
            
            // 检查是否支持断点续传
            val resumeOffset = if (task.supportResume && tempFile.exists() && tempFile.length() > RESUME_THRESHOLD_BYTES) {
                tempFile.length()
            } else {
                0L
            }
            
            // 构建请求
            val requestBuilder = Request.Builder().url(task.url)
            if (resumeOffset > 0) {
                requestBuilder.addHeader("Range", "bytes=$resumeOffset-")
                Log.d(TAG, "断点续传: ${task.taskId} 从 ${resumeOffset} 字节开始")
            }
            
            val request = requestBuilder.build()
            val response = client.newCall(request).execute()
            
            if (!response.isSuccessful) {
                return@withContext Result.failure(IOException("HTTP ${response.code}: ${response.message}"))
            }
            
            val body = response.body ?: return@withContext Result.failure(IOException("响应体为空"))
            val contentLength = body.contentLength()
            val totalBytes = if (resumeOffset > 0) resumeOffset + contentLength else contentLength
            
            // 更新总字节数
            updateDownloadState(task.taskId) { it.copy(totalBytes = totalBytes) }
            
            // 开始下载
            val outputStream = if (resumeOffset > 0) {
                FileOutputStream(tempFile, true) // 追加模式
            } else {
                FileOutputStream(tempFile, false) // 覆盖模式
            }

            var bytesDownloaded = resumeOffset
            val startTime = System.currentTimeMillis()
            var lastProgressTime = startTime

            outputStream.use { output ->
                body.byteStream().use { input ->
                    val buffer = ByteArray(8192)
                    var bytesRead: Int

                    while (input.read(buffer).also { bytesRead = it } != -1) {
                        output.write(buffer, 0, bytesRead)
                        bytesDownloaded += bytesRead

                        // 更新进度（限制更新频率）
                        val currentTime = System.currentTimeMillis()
                        if (currentTime - lastProgressTime > 100) { // 每100ms更新一次
                            val progress = if (totalBytes > 0) bytesDownloaded.toFloat() / totalBytes else 0f
                            val speed = if (currentTime > startTime) {
                                (bytesDownloaded - resumeOffset) * 1000 / (currentTime - startTime)
                            } else 0L

                            updateDownloadState(task.taskId) {
                                it.copy(
                                    progress = progress,
                                    bytesDownloaded = bytesDownloaded,
                                    speed = speed
                                )
                            }

                            task.onProgress(progress)
                            lastProgressTime = currentTime
                        }
                    }
                }
            }
            
            // 下载完成，重命名临时文件
            if (tempFile.renameTo(targetFile)) {
                totalBytesDownloaded.addAndGet(bytesDownloaded - resumeOffset)
                Result.success(targetFile)
            } else {
                Result.failure(IOException("重命名文件失败"))
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "下载执行异常: ${task.taskId}", e)
            Result.failure(e)
        }
    }
    
    /**
     * 更新下载状态
     */
    private fun updateDownloadState(taskId: String, update: (DownloadState) -> DownloadState) {
        downloadStates[taskId]?.let { currentState ->
            downloadStates[taskId] = update(currentState)
        }
    }
    
    /**
     * 暂停所有下载
     */
    private fun pauseAllDownloads() {
        activeDownloads.values.forEach { job ->
            job.cancel()
        }
        
        downloadStates.values.forEach { state ->
            if (state.status == DownloadStatus.DOWNLOADING) {
                updateDownloadState(state.taskId) { it.copy(status = DownloadStatus.PAUSED) }
            }
        }
        
        Log.d(TAG, "已暂停所有下载")
    }
    
    /**
     * 恢复所有下载
     */
    private fun resumeAllDownloads() {
        downloadStates.values.forEach { state ->
            if (state.status == DownloadStatus.PAUSED) {
                // 重新添加到队列
                val task = DownloadTask(
                    taskId = state.taskId,
                    url = state.url,
                    targetFile = state.targetFile,
                    priority = state.priority,
                    supportResume = true
                )
                downloadQueue.offer(task)
                updateDownloadState(state.taskId) { it.copy(status = DownloadStatus.QUEUED) }
            }
        }
        
        Log.d(TAG, "已恢复所有下载")
    }
    
    /**
     * 是否允许在移动网络下载
     */
    private fun shouldDownloadOnCellular(): Boolean {
        // 这里可以读取用户设置
        return NetworkConfig.isDownloadEnabled() // 简化实现
    }
    
    /**
     * 获取下载统计信息
     */
    fun getDownloadStats(): String {
        val activeCount = activeDownloads.size
        val queuedCount = downloadQueue.size
        
        return buildString {
            appendLine("=== 并发下载统计 ===")
            appendLine("网络状态: ${_networkState.value}")
            appendLine("活跃下载: $activeCount")
            appendLine("队列中任务: $queuedCount")
            appendLine("总下载任务: ${totalDownloads.get()}")
            appendLine("已完成: ${completedDownloads.get()}")
            appendLine("失败: ${failedDownloads.get()}")
            appendLine("总下载量: ${totalBytesDownloaded.get() / 1024 / 1024}MB")
            appendLine("==================")
        }
    }
    
    /**
     * 获取任务状态
     */
    fun getTaskState(taskId: String): DownloadState? {
        return downloadStates[taskId]
    }
    
    /**
     * 取消下载任务
     */
    fun cancelTask(taskId: String): Boolean {
        val job = activeDownloads[taskId]
        if (job != null) {
            job.cancel()
            updateDownloadState(taskId) { it.copy(status = DownloadStatus.CANCELLED) }
            return true
        }
        
        // 从队列中移除
        downloadQueue.removeIf { it.taskId == taskId }
        updateDownloadState(taskId) { it.copy(status = DownloadStatus.CANCELLED) }
        return false
    }
    
    /**
     * 销毁管理器
     */
    fun destroy() {
        Log.d(TAG, "🔚 销毁ConcurrentDownloadManager")
        
        // 取消所有下载
        activeDownloads.values.forEach { it.cancel() }
        activeDownloads.clear()
        
        // 清理队列
        downloadQueue.clear()
        downloadStates.clear()
        
        // 取消协程作用域
        downloadScope.cancel()
        
        // 关闭HTTP客户端
        client.dispatcher.executorService.shutdown()
    }
}
