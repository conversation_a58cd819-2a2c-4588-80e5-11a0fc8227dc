package com.example.coloringproject.manager

import android.graphics.Color
import android.graphics.drawable.GradientDrawable
import android.util.Log
import android.widget.Toast
import com.example.coloringproject.SimpleMainActivity
import com.example.coloringproject.adapter.ImmersiveColorAdapter
import com.example.coloringproject.callback.ColorChangeCallback
import com.example.coloringproject.data.ColorPalette
import com.example.coloringproject.data.Region
import com.example.coloringproject.databinding.ActivitySimpleMainBinding
import com.example.coloringproject.model.ColorInfo

/**
 * 颜色管理器
 * 负责颜色选择、进度计算和更新
 */
class ColorManager(
    activity: SimpleMainActivity,
    binding: ActivitySimpleMainBinding
) : BaseManager(activity, binding) {

    companion object {
        private const val TAG = "ColorManager"
    }

    private var callback: ColorChangeCallback? = null
    private var currentSelectedColor: ColorPalette? = null
    private var cachedProcessedPalette: List<ColorPalette> = emptyList()
    private lateinit var immersiveColorAdapter: ImmersiveColorAdapter

    /**
     * 设置颜色变化回调
     */
    fun setCallback(callback: ColorChangeCallback?) {
        this.callback = callback
    }

    /**
     * 设置沉浸式颜色适配器
     */
    fun setImmersiveColorAdapter(adapter: ImmersiveColorAdapter) {
        this.immersiveColorAdapter = adapter
    }

    /**
     * 获取当前选中的颜色
     */
    fun getCurrentSelectedColor(): ColorPalette? = currentSelectedColor

    /**
     * 获取缓存的处理后调色板
     */
    fun getCachedProcessedPalette(): List<ColorPalette> = cachedProcessedPalette

    /**
     * 选择颜色
     */
    fun selectColor(color: ColorPalette) {
        Log.d(TAG, "Selecting color: ${color.colorHex} (${color.name})")

        currentSelectedColor = color

        // 找到处理后的调色板中的索引（使用缓存）
        val colorIndex = cachedProcessedPalette.indexOfFirst {
            normalizeColorHex(it.colorHex) == normalizeColorHex(color.colorHex)
        }
        if (colorIndex != -1) {
            // 更新沉浸式适配器的选中状态
            val colorInfo = ColorInfo(
                id = colorIndex,
                name = color.name,
                hexColor = color.colorHex
            )
            immersiveColorAdapter.setSelectedColor(colorInfo)
            Log.d(TAG, "选中颜色索引: $colorIndex, 颜色: ${color.name}")
        } else {
            Log.w(TAG, "未找到颜色索引: ${color.colorHex}")
        }

        // 设置当前颜色到ColoringView，这会触发马赛克显示
        binding.coloringView.setCurrentColor(color.colorHex)

        // 更新当前颜色显示
        val drawable = GradientDrawable().apply {
            shape = GradientDrawable.OVAL
            setColor(Color.parseColor(color.colorHex))
            setStroke(4, Color.GRAY)
        }
        binding.currentColorView.background = drawable

        // 计算并显示当前选中颜色的进度
        updateCurrentColorDisplay(color)
        updateCurrentSelectedColorProgress()

        // 确保马赛克提醒开启
        binding.coloringView.setShowHints(true)

        // 通知回调
        callback?.onColorSelected(color.colorHex)

        Log.d(TAG, "颜色选择完成，已计算当前颜色进度: ${color.colorHex}")
    }

    /**
     * 根据颜色十六进制值自动选择颜色
     */
    fun autoSelectColorByHex(colorHex: String) {
        Log.d(TAG, "自动选择颜色: $colorHex")

        // 标准化颜色值
        val normalizedColorHex = normalizeColorHex(colorHex)

        // 检查是否是背景色或空白区域（白色、透明等）
        if (isBackgroundColor(normalizedColorHex)) {
            Log.d(TAG, "长按的是背景区域，不进行颜色选择")
            return
        }

        // 在缓存的调色板中查找匹配的颜色
        val matchingColor = cachedProcessedPalette.find { palette ->
            normalizeColorHex(palette.colorHex) == normalizedColorHex
        }

        if (matchingColor != null) {
            // 找到匹配的颜色，选择它
            selectColor(matchingColor)

            // 显示提示信息
            Toast.makeText(activity, "已选择颜色: ${matchingColor.name}", Toast.LENGTH_SHORT).show()

            Log.d(TAG, "成功自动选择颜色: ${matchingColor.name} ($normalizedColorHex)")
        } else {
            // 没有找到匹配的颜色，但不显示错误提示，因为可能是用户误触
            Log.d(TAG, "长按区域颜色不在调色板中: $normalizedColorHex")
        }
    }

    /**
     * 处理调色板，去重同色并计算进度
     */
    fun processColorPaletteWithProgress(
        originalPalette: List<ColorPalette>,
        regions: List<Region>,
        customFilledRegions: Set<Int>? = null
    ): List<ColorPalette> {
        // 使用传入的填色区域数据，如果没有则使用全局数据
        val targetFilledRegions = customFilledRegions ?: getFilledRegions()

        // 按标准化颜色分组区域
        val regionsByColor = regions.groupBy { region ->
            normalizeColorHex(region.colorHex)
        }

        // 去重颜色并计算进度
        val uniqueColors = mutableMapOf<String, ColorPalette>()

        for (palette in originalPalette) {
            val normalizedColorHex = normalizeColorHex(palette.colorHex)

            if (!uniqueColors.containsKey(normalizedColorHex)) {
                // 计算这种颜色的区域总数
                val colorRegions = regionsByColor[normalizedColorHex] ?: emptyList()
                val totalCount = colorRegions.size

                // 计算已填充的区域数
                val filledCount = colorRegions.count { region ->
                    targetFilledRegions.contains(region.id)
                }

                // 创建带进度信息的调色板项
                val paletteWithProgress = palette.copy(
                    filledCount = filledCount,
                    totalCount = totalCount
                )

                uniqueColors[normalizedColorHex] = paletteWithProgress

                Log.d(TAG, "Color ${palette.name} ($normalizedColorHex): $filledCount/$totalCount (using ${if (customFilledRegions != null) "custom" else "global"} filled regions)")
            }
        }

        val result = uniqueColors.values.toList().sortedBy { it.id }
        cachedProcessedPalette = result
        return result
    }

    /**
     * 简化的调色板处理（不计算进度）
     */
    fun processColorPaletteSimple(originalPalette: List<ColorPalette>): List<ColorPalette> {
        val result = originalPalette.map { palette ->
            ColorPalette(
                id = palette.id,
                colorHex = palette.colorHex,
                colorRgb = palette.colorRgb,
                name = palette.name,
                usageCount = palette.usageCount,
                filledCount = 0, // 不预计算
                totalCount = 0   // 不预计算
            )
        }
        cachedProcessedPalette = result
        return result
    }

    /**
     * 转换ColorPalette到ColorInfo
     */
    fun convertToColorInfo(colorPalette: List<ColorPalette>): List<ColorInfo> {
        return colorPalette.mapIndexed { index, palette ->
            ColorInfo(
                id = index,
                name = palette.name,
                hexColor = palette.colorHex
            )
        }
    }

    /**
     * 标准化颜色十六进制格式
     */
    fun normalizeColorHex(colorHex: String): String {
        var normalized = colorHex.trim().lowercase()
        if (!normalized.startsWith("#")) {
            normalized = "#$normalized"
        }
        // 确保是6位十六进制格式
        if (normalized.length == 4) {
            // #RGB -> #RRGGBB
            val r = normalized[1]
            val g = normalized[2]
            val b = normalized[3]
            normalized = "#$r$r$g$g$b$b"
        }
        return normalized
    }

    /**
     * 检查是否是背景色
     */
    private fun isBackgroundColor(normalizedColorHex: String): Boolean {
        val backgroundColors = setOf(
            "#FFFFFF", // 白色
            "#000000", // 黑色
            "#00FFFFFF", // 透明白色
            "#00000000", // 完全透明
            "#FFFFFFFF", // 不透明白色
            "#FF000000"  // 不透明黑色
        )
        return backgroundColors.contains(normalizedColorHex.uppercase())
    }

    /**
     * 更新当前颜色的显示信息
     */
    private fun updateCurrentColorDisplay(color: ColorPalette) {
        val coloringData = getCurrentColoringData() ?: return

        val normalizedColorHex = normalizeColorHex(color.colorHex)
        val colorRegions = coloringData.regions.filter { region ->
            normalizeColorHex(region.colorHex) == normalizedColorHex
        }
        val filledColorRegions = colorRegions.filter { region ->
            getFilledRegions().contains(region.id)
        }

        val progressText = "${filledColorRegions.size}/${colorRegions.size}"
        binding.tvCurrentColorName.text = "${color.name} ($progressText)"

        Log.d(TAG, "Updated current color display: ${color.name} = $progressText (normalized: $normalizedColorHex)")
    }

    /**
     * 更新当前选中颜色的进度（按需计算）
     */
    fun updateCurrentSelectedColorProgress() {
        val currentColor = currentSelectedColor ?: return
        val data = getCurrentColoringData() ?: return

        val startTime = System.currentTimeMillis()

        // 只计算当前选中颜色的进度
        val normalizedColorHex = normalizeColorHex(currentColor.colorHex)

        // 找到这种颜色的所有区域
        val colorRegions = data.regions.filter { region ->
            normalizeColorHex(region.colorHex) == normalizedColorHex
        }

        // 计算已填充的区域数
        val filledCount = colorRegions.count { region ->
            getFilledRegions().contains(region.id)
        }

        val totalCount = colorRegions.size
        val progress = if (totalCount > 0) {
            (filledCount * 100) / totalCount
        } else {
            0
        }

        // 找到当前颜色在缓存的调色板中的索引
        val colorIndex = cachedProcessedPalette.indexOfFirst { palette ->
            normalizeColorHex(palette.colorHex) == normalizedColorHex
        }

        if (colorIndex >= 0) {
            immersiveColorAdapter.updateColorProgress(colorIndex, progress)

            val calcTime = System.currentTimeMillis() - startTime
            Log.d(TAG, "单颜色进度计算耗时: ${calcTime}ms")
            Log.d(TAG, "更新当前选中颜色进度: ${currentColor.name} (index=$colorIndex) = $progress% ($filledCount/$totalCount)")
        }

        // 检查当前颜色是否完成，如果完成则自动切换
        if (progress == 100) {
            checkAndSwitchToNextColor()
            callback?.onColorCompleted(currentColor.colorHex, totalCount)
        }
    }

    /**
     * 检查并切换到下一个未完成的颜色
     */
    private fun checkAndSwitchToNextColor() {
        val data = getCurrentColoringData() ?: return

        // 使用缓存的调色板，避免重复计算
        val nextColor = cachedProcessedPalette.find { palette ->
            val normalizedColorHex = normalizeColorHex(palette.colorHex)
            val colorRegions = data.regions.filter { region ->
                normalizeColorHex(region.colorHex) == normalizedColorHex
            }
            val filledCount = colorRegions.count { region ->
                getFilledRegions().contains(region.id)
            }
            filledCount < colorRegions.size // 找到未完成的颜色
        }

        if (nextColor != null) {
            selectColor(nextColor)
            Log.d(TAG, "自动切换到下一个颜色: ${nextColor.name}")
        } else {
            Log.d(TAG, "所有颜色都已完成！")
            callback?.onProjectCompleted()
        }
    }
}
