package com.example.coloringproject.manager

import android.util.Log
import android.view.View
import android.widget.TextView
import com.example.coloringproject.R
import com.example.coloringproject.SimpleMainActivity
import com.example.coloringproject.callback.UIStateCallback
import com.example.coloringproject.databinding.ActivitySimpleMainBinding

/**
 * UI状态管理器
 * 负责管理Activity的各种UI状态切换
 */
class UIStateManager(
    activity: SimpleMainActivity,
    binding: ActivitySimpleMainBinding
) : BaseManager(activity, binding) {

    companion object {
        private const val TAG = "UIStateManager"
    }

    private var callback: UIStateCallback? = null

    /**
     * 设置UI状态回调
     */
    fun setCallback(callback: UIStateCallback?) {
        this.callback = callback
    }

    /**
     * 显示加载状态
     */
    fun showLoading() {
        try {
            binding.loadingIndicator.visibility = View.VISIBLE
            binding.tvStatus.visibility = View.GONE
            
            callback?.onShowLoading()
            Log.d(TAG, "显示加载状态")
        } catch (e: Exception) {
            Log.e(TAG, "显示加载状态失败", e)
        }
    }

    /**
     * 显示就绪状态
     */
    fun showReady() {
        try {
            binding.loadingIndicator.visibility = View.GONE
            binding.tvStatus.visibility = View.GONE
            
            callback?.onShowReady()
            Log.d(TAG, "显示就绪状态")
        } catch (e: Exception) {
            Log.e(TAG, "显示就绪状态失败", e)
        }
    }

    /**
     * 显示错误状态
     */
    fun showError(message: String) {
        try {
            binding.loadingIndicator.visibility = View.GONE
            binding.tvStatus.visibility = View.VISIBLE
            binding.tvStatus.text = message
            
            callback?.onShowError(message)
            Log.d(TAG, "显示错误状态: $message")
        } catch (e: Exception) {
            Log.e(TAG, "显示错误状态失败", e)
        }
    }

    /**
     * 显示加载指示器
     */
    fun showLoadingIndicator() {
        try {
            // 查找并显示可能的加载指示器
            val loadingIndicator = activity.findViewById<View>(R.id.loadingIndicator)
            loadingIndicator?.visibility = View.VISIBLE

            val progressBar = activity.findViewById<View>(R.id.progressBar)
            progressBar?.visibility = View.VISIBLE

            val loadingText = activity.findViewById<TextView>(R.id.tvStatus)
            loadingText?.apply {
                visibility = View.VISIBLE
                text = "正在加载项目..."
            }

            // 隐藏填色UI
            binding.recyclerViewColors.visibility = View.GONE

            Log.d(TAG, "加载指示器已显示")
        } catch (e: Exception) {
            Log.w(TAG, "显示加载指示器时出错", e)
        }
    }

    /**
     * 隐藏加载指示器
     */
    fun hideLoadingIndicator() {
        try {
            // 查找并隐藏可能的加载指示器
            val loadingIndicator = activity.findViewById<View>(R.id.loadingIndicator)
            loadingIndicator?.visibility = View.GONE

            val loadingText = activity.findViewById<View>(R.id.tvStatus)
            loadingText?.visibility = View.GONE

            Log.d(TAG, "加载指示器已隐藏")
        } catch (e: Exception) {
            Log.w(TAG, "隐藏加载指示器时出错", e)
        }
    }

    /**
     * 显示填色UI控件
     */
    fun showColoringUI() {
        try {
            // 显示颜色面板
            binding.recyclerViewColors.visibility = View.VISIBLE

            // 显示底部控件
            val bottomPanel = activity.findViewById<View>(R.id.bottomPanel)
            bottomPanel?.visibility = View.VISIBLE

            callback?.onShowColoringUI()
            Log.d(TAG, "填色UI已显示")
        } catch (e: Exception) {
            Log.w(TAG, "显示填色UI时出错", e)
        }
    }

    /**
     * 隐藏填色UI控件
     */
    fun hideColoringUI() {
        try {
            binding.recyclerViewColors.visibility = View.GONE
            
            val bottomPanel = activity.findViewById<View>(R.id.bottomPanel)
            bottomPanel?.visibility = View.GONE

            callback?.onHideColoringUI()
            Log.d(TAG, "填色UI已隐藏")
        } catch (e: Exception) {
            Log.w(TAG, "隐藏填色UI时出错", e)
        }
    }

    /**
     * 立即显示涂色界面
     */
    fun showColoringViewImmediately() {
        try {
            // 隐藏所有可能的加载UI元素
            hideAllLoadingElements()

            // 显示涂色界面
            binding.coloringView.visibility = View.VISIBLE
            binding.coloringView.alpha = 1f

            Log.d(TAG, "立即显示涂色界面")
        } catch (e: Exception) {
            Log.e(TAG, "立即显示涂色界面失败", e)
        }
    }

    /**
     * 隐藏所有加载相关的UI元素
     */
    fun hideAllLoadingElements() {
        try {
            // 隐藏可能存在的加载UI元素
            binding.loadingIndicator.visibility = View.GONE
            binding.progressBar.visibility = View.GONE

            Log.d(TAG, "所有加载UI元素已隐藏")
        } catch (e: Exception) {
            Log.e(TAG, "隐藏加载UI元素时出错", e)
        }
    }

    /**
     * 更新提醒次数显示
     */
    fun updateHintCountDisplay(hintCount: Int) {
        try {
            binding.tvHintCount.text = hintCount.toString()
            binding.tvHintCount.visibility = if (hintCount > 0) View.VISIBLE else View.GONE
            Log.d(TAG, "更新提醒次数显示: $hintCount")
        } catch (e: Exception) {
            Log.e(TAG, "更新提醒次数显示失败", e)
        }
    }

    /**
     * 更新进度显示
     */
    fun updateProgressDisplay(filled: Int, total: Int) {
        try {
            binding.tvProgress.text = "$filled / $total"
            
            val progressPercentage = if (total > 0) (filled * 100 / total) else 0
            binding.progressBar.progress = progressPercentage
            
            Log.d(TAG, "更新进度显示: $progressPercentage% ($filled/$total)")
        } catch (e: Exception) {
            Log.e(TAG, "更新进度显示失败", e)
            // 设置默认值，防止崩溃
            binding.tvProgress.text = "0 / 0"
            binding.progressBar.progress = 0
        }
    }

    /**
     * 更新项目状态显示
     */
    fun updateProjectStatus(message: String) {
        try {
            binding.tvStatus.text = message
            Log.d(TAG, "更新项目状态: $message")
        } catch (e: Exception) {
            Log.e(TAG, "更新项目状态失败", e)
        }
    }
}
