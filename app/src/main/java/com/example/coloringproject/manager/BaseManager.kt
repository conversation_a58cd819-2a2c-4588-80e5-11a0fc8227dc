package com.example.coloringproject.manager

import android.content.Context
import androidx.lifecycle.LifecycleCoroutineScope
import com.example.coloringproject.SimpleMainActivity
import com.example.coloringproject.data.ColoringData
import com.example.coloringproject.databinding.ActivitySimpleMainBinding

/**
 * 管理器基类
 * 提供所有管理器的通用功能和数据访问
 */
abstract class BaseManager(
    protected val activity: SimpleMainActivity,
    protected val binding: ActivitySimpleMainBinding
) {
    protected val context: Context get() = activity
    protected val lifecycleScope: LifecycleCoroutineScope get() = activity.lifecycleScope
    
    /**
     * 获取当前填色数据
     */
    protected fun getCurrentColoringData(): ColoringData? {
        return activity.getCurrentColoringData()
    }
    
    /**
     * 获取已填色区域集合
     */
    protected fun getFilledRegions(): MutableSet<Int> {
        return activity.getFilledRegions()
    }
    
    /**
     * 更新已填色区域集合
     */
    protected fun updateFilledRegions(regions: Set<Int>) {
        activity.updateFilledRegions(regions)
    }
    
    /**
     * 获取标准化项目名称
     */
    protected fun getStandardizedProjectName(): String {
        return activity.getStandardizedProjectName()
    }
    
    /**
     * 检查Activity是否已销毁
     */
    protected fun isActivityDestroyed(): Boolean {
        return activity.isDestroyed || activity.isFinishing
    }
    
    /**
     * 在主线程执行操作
     */
    protected fun runOnUiThread(action: () -> Unit) {
        activity.runOnUiThread(action)
    }
    
    /**
     * 管理器初始化方法，子类可重写
     */
    open fun initialize() {
        // 默认空实现
    }
    
    /**
     * 管理器清理方法，子类可重写
     */
    open fun cleanup() {
        // 默认空实现
    }
}
