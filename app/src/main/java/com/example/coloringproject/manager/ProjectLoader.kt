package com.example.coloringproject.manager

import android.util.Log
import com.example.coloringproject.BuildConfig
import com.example.coloringproject.SimpleMainActivity
import com.example.coloringproject.callback.ProjectLoadCallback
import com.example.coloringproject.databinding.ActivitySimpleMainBinding
import com.example.coloringproject.utils.EnhancedAssetManager
import com.example.coloringproject.utils.HybridResourceManager
import com.example.coloringproject.utils.ProjectPreloadManager
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.withContext

/**
 * 项目加载器
 * 负责所有项目加载相关的逻辑
 */
class ProjectLoader(
    activity: SimpleMainActivity,
    binding: ActivitySimpleMainBinding
) : BaseManager(activity, binding) {

    companion object {
        private const val TAG = "ProjectLoader"
    }

    private var callback: ProjectLoadCallback? = null
    private val enhancedAssetManager = EnhancedAssetManager(activity)
    private val hybridResourceManager = HybridResourceManager(activity)
    private val preloadManager = ProjectPreloadManager.getInstance(activity)

    /**
     * 设置项目加载回调
     */
    fun setCallback(callback: ProjectLoadCallback?) {
        this.callback = callback
    }

    /**
     * 加载指定名称的项目
     */
    fun loadSpecificProject(projectName: String) {
        lifecycleScope.launch {
            try {
                Log.i(TAG, "开始加载指定项目: $projectName")
                callback?.onLoadingStarted()

                val projectsResult = enhancedAssetManager.getValidatedProjects()
                if (projectsResult.isFailure) {
                    Log.e(TAG, "项目验证失败", projectsResult.exceptionOrNull())
                    runOnUiThread {
                        callback?.onProjectLoadError("项目验证失败", "项目验证失败: ${projectsResult.exceptionOrNull()?.message}")
                    }
                    return@launch
                }

                val projects = projectsResult.getOrNull()!!
                val targetProject = projects.find { it.name == projectName && it.isValid }

                if (targetProject == null) {
                    Log.w(TAG, "未找到指定项目: $projectName")
                    runOnUiThread {
                        callback?.onProjectLoadError("项目未找到", "未找到项目: $projectName")
                        // 回退到加载第一个有效项目
                        loadFirstValidatedProject()
                    }
                    return@launch
                }

                Log.i(TAG, "找到目标项目: ${targetProject.name}")
                loadValidatedProject(targetProject)

            } catch (e: Exception) {
                Log.e(TAG, "加载指定项目失败: $projectName", e)
                runOnUiThread {
                    callback?.onProjectLoadError("加载失败", "加载项目失败: ${e.message}")
                }
            }
        }
    }

    /**
     * 从Gallery加载项目（优先从保存的进度加载）
     */
    fun loadProjectFromGallery(projectName: String, hasProgress: Boolean) {
        lifecycleScope.launch {
            try {
                val startTime = System.currentTimeMillis()
                Log.i(TAG, "🚀 [性能] Gallery加载开始: $projectName, 有进度: $hasProgress")
                callback?.onLoadingStarted()

                if (hasProgress) {
                    // 步骤1：并行加载进度数据和outline
                    val step1Start = System.currentTimeMillis()
                    val projectSaveManager = com.example.coloringproject.utils.ProjectSaveManager(activity)

                    val progressDeferred = async {
                        val progressStart = System.currentTimeMillis()
                        val result = projectSaveManager.loadFullProgress(projectName)
                        val progressTime = System.currentTimeMillis() - progressStart
                        Log.d(TAG, "📊 [性能] 进度数据加载耗时: ${progressTime}ms")
                        result
                    }
                    val outlineDeferred = async {
                        val outlineStart = System.currentTimeMillis()
                        val result = enhancedAssetManager.loadOutlineBitmap("$projectName.png")
                        val outlineTime = System.currentTimeMillis() - outlineStart
                        Log.d(TAG, "📊 [性能] Outline图片加载耗时: ${outlineTime}ms")
                        result
                    }

                    val progressResult = progressDeferred.await()
                    val outlineResult = outlineDeferred.await()
                    val step1Time = System.currentTimeMillis() - step1Start
                    Log.d(TAG, "📊 [性能] 步骤1-并行加载总耗时: ${step1Time}ms")

                    when (progressResult) {
                        is com.example.coloringproject.utils.LoadResult.Success -> {
                            val fullProgressData = progressResult.data

                            outlineResult.fold(
                                onSuccess = { outlineBitmap ->
                                    val dataLoadTime = System.currentTimeMillis() - startTime
                                    Log.d(TAG, "📊 [性能] 数据加载完成，耗时: ${dataLoadTime}ms")

                                    runOnUiThread {
                                        callback?.onLoadingCompleted()
                                        callback?.onProjectLoaded(fullProgressData.coloringData, outlineBitmap)

                                        val totalTime = System.currentTimeMillis() - startTime
                                        Log.i(TAG, "✅ [性能] Gallery快速加载完成: $projectName (总耗时: ${totalTime}ms)")

                                        // 性能分析
                                        if (totalTime > 300) {
                                            Log.w(TAG, "⚠️ [性能] 加载超过300ms目标，需要优化")
                                        } else {
                                            Log.i(TAG, "🎯 [性能] 加载在300ms内完成！")
                                        }
                                    }
                                },
                                onFailure = { error ->
                                    Log.w(TAG, "加载outline失败，回退到普通加载: $error")
                                    loadSpecificProject(projectName)
                                }
                            )
                            return@launch
                        }
                        is com.example.coloringproject.utils.LoadResult.Error -> {
                            Log.w(TAG, "无法从保存进度加载，回退到普通加载: ${progressResult.message}")
                        }
                    }
                }

                // 如果没有进度或加载进度失败，使用普通方式加载
                Log.i(TAG, "使用普通方式加载项目: $projectName")
                loadSpecificProject(projectName)

            } catch (e: Exception) {
                Log.e(TAG, "从Gallery加载项目失败: $projectName", e)
                runOnUiThread {
                    callback?.onProjectLoadError("Gallery加载失败", "加载项目失败: ${e.message}")
                }
            }
        }
    }

    /**
     * 加载第一个验证过的项目
     */
    fun loadFirstValidatedProject() {
        lifecycleScope.launch {
            try {
                Log.i(TAG, "开始验证和加载项目...")
                callback?.onLoadingStarted()

                val projectsResult = enhancedAssetManager.getValidatedProjects()
                if (projectsResult.isFailure) {
                    Log.e(TAG, "项目验证失败", projectsResult.exceptionOrNull())
                    runOnUiThread {
                        callback?.onProjectLoadError("项目验证失败", "项目验证失败: ${projectsResult.exceptionOrNull()?.message}")
                    }
                    return@launch
                }

                val projects = projectsResult.getOrNull()!!
                val validProjects = projects.filter { it.isValid }

                Log.i(TAG, "项目验证完成: 总计 ${projects.size} 个项目, 有效 ${validProjects.size} 个")

                // 显示验证报告
                val report = enhancedAssetManager.getValidationReport(projects)
                Log.i(TAG, "验证报告:\n$report")

                if (validProjects.isEmpty()) {
                    Log.e(TAG, "没有找到有效的项目")
                    runOnUiThread {
                        callback?.onProjectLoadError("无有效项目", "没有找到有效的填色项目\n\n检查assets文件夹中是否有匹配的JSON和PNG文件")
                    }
                    return@launch
                }

                // 加载第一个有效项目
                val firstProject = validProjects.first()
                Log.i(TAG, "加载项目: ${firstProject.name}")
                loadValidatedProject(firstProject)

                // 如果有多个项目，在UI中显示选择选项
                if (validProjects.size > 1) {
                    runOnUiThread {
                        callback?.onShowProjectSelector(validProjects)
                    }
                }

            } catch (e: Exception) {
                Log.e(TAG, "加载项目失败", e)
                runOnUiThread {
                    callback?.onProjectLoadError("加载失败", "加载项目失败: ${e.message}")
                }
            }
        }
    }

    /**
     * 加载验证过的项目
     */
    suspend fun loadValidatedProject(project: EnhancedAssetManager.ValidatedProject) {
        // 显示加载状态
        runOnUiThread {
            callback?.onLoadingStarted()
        }

        try {
            Log.i(TAG, "正在加载项目: ${project.name}")
            Log.i(TAG, "项目信息: ${project.totalRegions}个区域, ${project.totalColors}种颜色, ${project.fileSize.totalSizeKB}KB")

            // 加载填色数据
            val coloringDataResult = enhancedAssetManager.loadColoringData(project.jsonFile)
            if (coloringDataResult.isFailure) {
                throw Exception("加载JSON失败: ${coloringDataResult.exceptionOrNull()?.message}")
            }

            val coloringData = coloringDataResult.getOrNull()!!

            // 加载outline图片
            val outlineBitmapResult = enhancedAssetManager.loadOutlineBitmap(project.outlineFile)
            if (outlineBitmapResult.isFailure) {
                throw Exception("加载outline图片失败: ${outlineBitmapResult.exceptionOrNull()?.message}")
            }

            val outlineBitmap = outlineBitmapResult.getOrNull()!!

            // 在主线程更新UI
            runOnUiThread {
                callback?.onLoadingCompleted()
                callback?.onProjectLoaded(coloringData, outlineBitmap)
                Log.i(TAG, "✅ 项目加载成功: ${project.name}")
            }

        } catch (e: Exception) {
            Log.e(TAG, "加载项目失败: ${project.name}", e)
            runOnUiThread {
                callback?.onProjectLoadError("项目加载失败", "加载项目失败: ${e.message}")
            }
        }
    }

    /**
     * 根据项目ID和来源加载项目
     */
    fun loadProjectById(projectId: String, projectSource: String?) {
        Log.d(TAG, "加载项目: ID=$projectId, Source=$projectSource")
        lifecycleScope.launch {
            try {
                // 显示加载状态
                runOnUiThread {
                    callback?.onLoadingStarted()
                }

                // 根据项目来源选择加载方式
                Log.i(TAG, "项目来源判断: $projectSource")
                when (projectSource) {
                    "BUILT_IN" -> {
                        Log.d(TAG, "加载内置项目: $projectId")
                        // 从Assets加载内置项目
                        loadBuiltInProject(projectId)
                    }
                    "REMOTE_DOWNLOADED" -> {
                        Log.d(TAG, "加载已下载项目: $projectId")
                        // 从下载目录加载
                        loadDownloadedProject(projectId)
                    }
                    "STREAMING" -> {
                        Log.d(TAG, "加载流式项目: $projectId")
                        // 流式下载项目
                        downloadAndLoadRemoteProject(projectId)
                    }
                    else -> {
                        Log.d(TAG, "使用混合管理器加载项目: $projectId")
                        // 使用混合资源管理器
                        loadProjectWithHybridManager(projectId)
                    }
                }

            } catch (e: Exception) {
                Log.e(TAG, "加载项目时出错: $projectId", e)
                runOnUiThread {
                    callback?.onProjectLoadError("项目加载异常", "加载项目 $projectId 时发生异常: ${e.message}\n\n将自动加载默认项目。")
                    // 回退到加载默认项目
                    loadFirstValidatedProject()
                }
            }
        }
    }
}
