package com.example.coloringproject.manager

import android.graphics.Bitmap
import android.util.Log
import android.view.View
import androidx.recyclerview.widget.LinearLayoutManager
import com.example.coloringproject.SimpleMainActivity
import com.example.coloringproject.adapter.ImmersiveColorAdapter
import com.example.coloringproject.data.ColoringData
import com.example.coloringproject.databinding.ActivitySimpleMainBinding
import com.example.coloringproject.model.ColorInfo
import com.example.coloringproject.utils.ProjectPreloadManager

/**
 * 项目设置管理器
 * 负责项目初始化和设置
 */
class ProjectSetupManager(
    activity: SimpleMainActivity,
    binding: ActivitySimpleMainBinding
) : BaseManager(activity, binding) {

    companion object {
        private const val TAG = "ProjectSetupManager"
    }

    private lateinit var immersiveColorAdapter: ImmersiveColorAdapter
    private var colorManager: ColorManager? = null
    private var progressManager: ProgressManager? = null
    private var uiStateManager: UIStateManager? = null

    /**
     * 设置依赖的管理器
     */
    fun setDependencies(
        colorManager: ColorManager,
        progressManager: ProgressManager,
        uiStateManager: UIStateManager
    ) {
        this.colorManager = colorManager
        this.progressManager = progressManager
        this.uiStateManager = uiStateManager
    }

    /**
     * 设置沉浸式颜色适配器
     */
    fun setImmersiveColorAdapter(adapter: ImmersiveColorAdapter) {
        this.immersiveColorAdapter = adapter
    }

    /**
     * 设置项目
     */
    fun setupProject(coloringData: ColoringData, bitmap: Bitmap) {
        val setupStart = System.currentTimeMillis()
        Log.d(TAG, "🔧 [性能] Setting up project - 开始")

        // 步骤1：基础数据设置
        val step1Start = System.currentTimeMillis()
        activity.currentColoringData = coloringData
        getFilledRegions().clear()
        val step1Time = System.currentTimeMillis() - step1Start
        Log.d(TAG, "📊 [性能] 基础数据设置耗时: ${step1Time}ms")

        // 步骤2：ColoringView设置
        val step2Start = System.currentTimeMillis()
        binding.coloringView.setColoringData(coloringData, bitmap)
        val step2Time = System.currentTimeMillis() - step2Start
        Log.d(TAG, "📊 [性能] ColoringView设置耗时: ${step2Time}ms")

        // 设置ColoringView回调
        setupColoringViewCallbacks()

        // 设置提醒状态 - 确保马赛克提醒开启
        binding.coloringView.setShowHints(true)
        binding.coloringView.setHintAlpha(0.4f)

        // 步骤3：处理调色板（最耗时的操作）
        val step3Start = System.currentTimeMillis()
        val cachedProcessedPalette = colorManager?.processColorPaletteWithProgress(
            coloringData.colorPalette, 
            coloringData.regions
        ) ?: emptyList()
        val step3Time = System.currentTimeMillis() - step3Start
        Log.d(TAG, "📊 [性能] 调色板处理耗时: ${step3Time}ms")

        // 步骤4：设置UI组件
        val step4Start = System.currentTimeMillis()
        val colorInfoList = colorManager?.convertToColorInfo(cachedProcessedPalette) ?: emptyList()
        immersiveColorAdapter.updateColors(colorInfoList, preserveProgress = false)
        val step4Time = System.currentTimeMillis() - step4Start
        Log.d(TAG, "📊 [性能] 颜色适配器更新耗时: ${step4Time}ms")

        // 步骤5：初始化颜色进度
        val step5Start = System.currentTimeMillis()
        colorManager?.updateAllColorProgress()
        val step5Time = System.currentTimeMillis() - step5Start
        Log.d(TAG, "📊 [性能] 颜色进度初始化耗时: ${step5Time}ms")

        // 选择第一个颜色
        if (cachedProcessedPalette.isNotEmpty()) {
            colorManager?.selectColor(cachedProcessedPalette.first())
        }

        // 更新进度
        updateProgress(coloringData)
        
        // 显示项目信息
        uiStateManager?.updateProjectStatus("项目已加载: ${coloringData.metadata.totalRegions}个区域")

        // 步骤6：显示UI
        val step6Start = System.currentTimeMillis()
        uiStateManager?.showReady()
        uiStateManager?.showColoringUI()
        val step6Time = System.currentTimeMillis() - step6Start
        Log.d(TAG, "📊 [性能] UI显示耗时: ${step6Time}ms")

        val totalTime = System.currentTimeMillis() - setupStart
        Log.d(TAG, "🎯 [性能] 项目设置总耗时: ${totalTime}ms")
        Log.d(TAG, "✅ Project setup completed")
    }

    /**
     * 快速设置项目（用于Gallery加载，跳过重复初始化）
     */
    fun setupProjectFast(coloringData: ColoringData, outlineBitmap: Bitmap, savedFilledRegions: Set<Int>) {
        val setupStart = System.currentTimeMillis()
        Log.d(TAG, "🔧 [性能] Fast project setup started")

        // 步骤1：设置基础数据
        val step1Start = System.currentTimeMillis()
        activity.currentColoringData = coloringData
        binding.coloringView.setColoringData(coloringData, outlineBitmap)
        val step1Time = System.currentTimeMillis() - step1Start
        Log.d(TAG, "📊 [性能] 设置ColoringView耗时: ${step1Time}ms")

        // 步骤2：安全恢复填色状态
        val step2Start = System.currentTimeMillis()
        val restoreSuccess = binding.coloringView.restoreProgressSafely(savedFilledRegions)
        val actualFilledRegions = if (restoreSuccess) {
            val restored = binding.coloringView.getFilledRegions()
            getFilledRegions().clear()
            getFilledRegions().addAll(restored)
            restored
        } else {
            Log.w(TAG, "Fast setup: Failed to restore progress, starting fresh")
            getFilledRegions().clear()
            emptySet<Int>()
        }
        val step2Time = System.currentTimeMillis() - step2Start
        Log.d(TAG, "📊 [性能] 安全恢复填色状态耗时: ${step2Time}ms")

        // 优化：简化调色板初始化，不计算所有颜色进度
        val step3Start = System.currentTimeMillis()
        val cachedProcessedPalette = colorManager?.processColorPaletteSimple(coloringData.colorPalette) ?: emptyList()
        val colorInfoList = colorManager?.convertToColorInfo(cachedProcessedPalette) ?: emptyList()

        // 设置适配器为只显示选中颜色进度模式
        immersiveColorAdapter.setShowOnlySelectedProgress(true)
        immersiveColorAdapter.updateColors(colorInfoList, preserveProgress = false)

        Log.d(TAG, "🚀 [性能优化] 跳过全量进度计算，只在颜色选择时计算单个进度")

        val step3Time = System.currentTimeMillis() - step3Start
        Log.d(TAG, "📊 [性能] 设置调色板和进度耗时: ${step3Time}ms")

        // 验证进度设置是否正确
        colorManager?.validateAdapterProgress()

        // 强制刷新适配器，解决视图复用问题
        immersiveColorAdapter.forceRefreshAll()

        // 选择颜色
        val step5Start = System.currentTimeMillis()
        val firstUnfinishedColor = cachedProcessedPalette.find { palette ->
            val colorRegions = coloringData.regions.filter { region ->
                colorManager?.normalizeColorHex(region.colorHex) == colorManager?.normalizeColorHex(palette.colorHex)
            }
            val filledCount = colorRegions.count { region ->
                savedFilledRegions.contains(region.id)
            }
            filledCount < colorRegions.size
        }

        if (firstUnfinishedColor != null) {
            colorManager?.selectColor(firstUnfinishedColor)
        } else if (cachedProcessedPalette.isNotEmpty()) {
            colorManager?.selectColor(cachedProcessedPalette.first())
        }
        val step5Time = System.currentTimeMillis() - step5Start
        Log.d(TAG, "📊 [性能] 选择颜色耗时: ${step5Time}ms")

        // 显示UI
        val step6Start = System.currentTimeMillis()
        uiStateManager?.updateProjectStatus("项目已加载: ${coloringData.metadata.totalRegions}个区域")
        uiStateManager?.showReady()
        uiStateManager?.showColoringUI()
        val step6Time = System.currentTimeMillis() - step6Start
        Log.d(TAG, "📊 [性能] 显示UI耗时: ${step6Time}ms")

        val totalSetupTime = System.currentTimeMillis() - setupStart
        Log.d(TAG, "🔧 [性能] Fast project setup completed, 总耗时: ${totalSetupTime}ms")

        // 性能分析
        if (totalSetupTime > 100) {
            Log.w(TAG, "⚠️ [性能] setupProjectFast超过100ms，需要优化")
        }
    }

    /**
     * 使用预加载数据初始化涂色界面
     */
    fun initializeWithPreloadedData(preloadedData: ProjectPreloadManager.PreloadedProjectData) {
        try {
            Log.d(TAG, "开始使用预加载数据初始化: ${preloadedData.projectId}")

            // 设置涂色数据
            activity.currentColoringData = preloadedData.coloringData
            binding.coloringView.setColoringData(preloadedData.coloringData, preloadedData.outlineBitmap)

            // 设置预处理的调色板
            updateColorPaletteWithPreprocessed(preloadedData.processedPalette)

            // 立即恢复进度（同步执行，确保在显示界面前完成）
            progressManager?.restoreProgressSync(preloadedData.projectId)

            // 重要：完成完整的UI初始化
            completeUIInitialization()

            // 立即显示涂色界面，跳过过渡动画和加载指示器
            uiStateManager?.showColoringViewImmediately()

            Log.d(TAG, "预加载数据初始化完成: ${preloadedData.projectId}")

        } catch (e: Exception) {
            Log.e(TAG, "预加载数据初始化失败: ${preloadedData.projectId}", e)
            // 回退到常规加载
            // 这里需要通过回调通知加载失败
        }
    }

    /**
     * 设置ColoringView回调
     */
    private fun setupColoringViewCallbacks() {
        binding.coloringView.onProjectCompleted = {
            // 整个项目完成时的处理
            runOnUiThread {
                showCompletionDialog()
            }
        }

        binding.coloringView.onColorCompleted = { colorHex, regionCount ->
            // 某种颜色全部完成时的处理
            runOnUiThread {
                colorManager?.updateCurrentSelectedColorProgress()
            }
        }

        binding.coloringView.onColorAutoSelected = { colorHex ->
            // 长按自动选择颜色时的处理
            runOnUiThread {
                colorManager?.autoSelectColorByHex(colorHex)
            }
        }
    }

    /**
     * 更新进度显示
     */
    private fun updateProgress(coloringData: ColoringData) {
        val totalRegions = coloringData.metadata.totalRegions
        val filled = getFilledRegions().size
        uiStateManager?.updateProgressDisplay(filled, totalRegions)
    }

    /**
     * 完成完整的UI初始化
     */
    private fun completeUIInitialization() {
        try {
            Log.d(TAG, "开始完成UI初始化")

            // 1. 设置长按监听器
            setupLongPressListener()

            // 2. 更新所有UI状态
            updateAllUIStates()

            // 3. 设置默认选中颜色（在适配器更新后）
            binding.recyclerViewColors.post {
                colorManager?.selectDefaultColor()
            }

            Log.d(TAG, "UI初始化完成")
        } catch (e: Exception) {
            Log.e(TAG, "UI初始化失败", e)
        }
    }

    /**
     * 设置长按监听器
     */
    private fun setupLongPressListener() {
        try {
            // ColoringView已经有长按自动选择颜色的功能
            binding.coloringView.onColorAutoSelected = { colorHex ->
                runOnUiThread {
                    colorManager?.autoSelectColorByHex(colorHex)
                }
            }
            Log.d(TAG, "长按监听器设置完成")
        } catch (e: Exception) {
            Log.e(TAG, "设置长按监听器失败", e)
        }
    }

    /**
     * 更新所有UI状态
     */
    private fun updateAllUIStates() {
        try {
            // 更新进度显示
            getCurrentColoringData()?.let { updateProgress(it) }

            // 更新所有颜色的进度
            colorManager?.updateAllColorProgress()

            Log.d(TAG, "所有UI状态更新完成")
        } catch (e: Exception) {
            Log.e(TAG, "更新UI状态失败", e)
        }
    }

    /**
     * 使用预处理的调色板更新UI
     */
    private fun updateColorPaletteWithPreprocessed(processedPalette: List<com.example.coloringproject.data.ColorPalette>) {
        try {
            Log.d(TAG, "开始使用预处理调色板更新UI，颜色数量: ${processedPalette.size}")

            // 转换为ColorInfo列表
            val colorInfoList = colorManager?.convertToColorInfo(processedPalette) ?: emptyList()

            // 更新适配器颜色列表
            immersiveColorAdapter.updateColors(colorInfoList, preserveProgress = false)

            // 更新所有颜色的进度
            processedPalette.forEachIndexed { index, palette ->
                val progress = if (palette.totalCount > 0) {
                    (palette.filledCount * 100) / palette.totalCount
                } else {
                    0
                }
                immersiveColorAdapter.updateColorProgress(index, progress)
                Log.d(TAG, "设置颜色进度: ${palette.name} (index=$index) = $progress%")
            }

            // 强制刷新适配器
            immersiveColorAdapter.forceRefreshAll()

            Log.d(TAG, "预处理调色板更新完成，颜色数量: ${processedPalette.size}")
        } catch (e: Exception) {
            Log.e(TAG, "更新预处理调色板失败", e)
        }
    }

    /**
     * 显示完成对话框
     */
    private fun showCompletionDialog() {
        // 这里应该通过回调通知Activity显示完成对话框
        Log.d(TAG, "项目完成！")
    }
}
