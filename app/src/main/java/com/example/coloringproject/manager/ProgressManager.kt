package com.example.coloringproject.manager

import android.graphics.Bitmap
import android.os.Handler
import android.os.Looper
import android.util.Log
import com.example.coloringproject.SimpleMainActivityRefactored
import com.example.coloringproject.callback.ProgressCallback
import com.example.coloringproject.databinding.ActivitySimpleMainBinding
import com.example.coloringproject.utils.LibraryEventManager
import com.example.coloringproject.utils.ProjectSaveManager
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

/**
 * 进度管理器
 * 负责项目进度的保存、恢复和预览图生成
 */
class ProgressManager(
    activity: SimpleMainActivityRefactored,
    binding: ActivitySimpleMainBinding
) : BaseManager(activity, binding) {

    companion object {
        private const val TAG = "ProgressManager"
    }

    private var callback: ProgressCallback? = null
    private val projectSaveManager = ProjectSaveManager(activity)
    
    // 预览图片生成的延迟任务
    private var previewGenerationRunnable: Runnable? = null
    private val previewGenerationHandler = Handler(Looper.getMainLooper())

    /**
     * 设置进度回调
     */
    fun setCallback(callback: ProgressCallback?) {
        this.callback = callback
    }

    /**
     * 保存当前进度（高效版本，1秒内完成）
     */
    fun saveCurrentProgress() {
        val coloringData = getCurrentColoringData()
        if (coloringData == null) {
            return
        }

        val projectName = getStandardizedProjectName()
        Log.d(TAG, "开始快速保存进度: $projectName")

        // 在后台线程执行保存任务，使用try-catch处理协程取消
        lifecycleScope.launch(Dispatchers.IO) {
            try {
                // 检查协程是否已被取消
                if (!isActive) {
                    Log.d(TAG, "协程已取消，跳过进度保存: $projectName")
                    return@launch
                }

                val startTime = System.currentTimeMillis()

                // 只保存必要的进度数据，不生成预览图片（太耗时）
                val result = projectSaveManager.saveProgressFast(
                    projectName = projectName,
                    coloringData = coloringData,
                    filledRegions = getFilledRegions().toSet() // 创建副本避免并发问题
                )

                val endTime = System.currentTimeMillis()
                Log.d(TAG, "快速保存完成，耗时: ${endTime - startTime}ms")

                // 检查协程是否仍然活跃
                if (isActive) {
                    // 在主线程处理结果
                    withContext(Dispatchers.Main) {
                        when (result) {
                            is com.example.coloringproject.utils.SaveResult.Success -> {
                                Log.d(TAG, "Progress saved successfully for project: $projectName")
                                callback?.onProgressSaved(projectName)

                                // 通知Library刷新项目进度
                                val progressPercentage = (getFilledRegions().size * 100) / (coloringData.regions.size)
                                Log.d(TAG, "异步保存成功，发送进度通知: $projectName, 进度: $progressPercentage%")
                                LibraryEventManager.notifyProjectProgressUpdated(
                                    projectName,
                                    getFilledRegions().isNotEmpty(),
                                    progressPercentage
                                )
                            }
                            is com.example.coloringproject.utils.SaveResult.Error -> {
                                Log.e(TAG, "Failed to save progress for project: $projectName")
                                callback?.onProgressSaveError(projectName, result.message)
                            }
                        }
                    }
                }
            } catch (e: Exception) {
                handleCoroutineException("进度保存: $projectName", e, isActive)
            }
        }
    }

    /**
     * 同步保存当前进度 - 用于退出前确保保存完成
     */
    suspend fun saveCurrentProgressSync() = withContext(Dispatchers.IO) {
        val projectName = getStandardizedProjectName()
        val coloringData = getCurrentColoringData() ?: return@withContext

        val startTime = System.currentTimeMillis()

        // 同步保存进度数据
        val result = projectSaveManager.saveProgressFast(
            projectName = projectName,
            coloringData = coloringData,
            filledRegions = getFilledRegions().toSet()
        )

        val endTime = System.currentTimeMillis()
        Log.d(TAG, "同步保存完成，耗时: ${endTime - startTime}ms")

        // 在主线程处理结果
        withContext(Dispatchers.Main) {
            when (result) {
                is com.example.coloringproject.utils.SaveResult.Success -> {
                    Log.d(TAG, "Progress saved successfully for project: $projectName")
                    callback?.onProgressSaved(projectName)

                    // 通知Library刷新项目进度
                    val progressPercentage = (getFilledRegions().size * 100) / (coloringData.regions.size)
                    Log.d(TAG, "同步保存成功，发送进度通知: $projectName, 进度: $progressPercentage%")
                    LibraryEventManager.notifyProjectProgressUpdated(
                        projectName,
                        getFilledRegions().isNotEmpty(),
                        progressPercentage
                    )
                }
                is com.example.coloringproject.utils.SaveResult.Error -> {
                    Log.e(TAG, "Failed to save progress for project: $projectName")
                    callback?.onProgressSaveError(projectName, result.message)
                }
            }
        }
    }

    /**
     * 加载保存的进度
     */
    fun loadSavedProgress(projectName: String) {
        Log.d(TAG, "=== 开始加载进度 ===")
        Log.d(TAG, "项目名称: $projectName")

        // 检查进度文件是否存在
        val progressFile = java.io.File(activity.getExternalFilesDir(null), "progress/${projectName}_progress.json")
        Log.d(TAG, "进度文件路径: ${progressFile.absolutePath}")
        Log.d(TAG, "进度文件存在: ${progressFile.exists()}")

        if (progressFile.exists()) {
            Log.d(TAG, "进度文件大小: ${progressFile.length()} bytes")
        }

        val result = projectSaveManager.loadFullProgress(projectName)

        when (result) {
            is com.example.coloringproject.utils.LoadResult.Success -> {
                val fullProgressData = result.data

                // 使用安全的进度恢复方法
                val restoreSuccess = binding.coloringView.restoreProgressSafely(fullProgressData.filledRegions)

                if (restoreSuccess) {
                    // 获取ColoringView中实际恢复的填色区域
                    val actualFilledRegions = binding.coloringView.getFilledRegions()

                    // 更新本地填色区域集合
                    updateFilledRegions(actualFilledRegions)

                    callback?.onProgressRestored(projectName, actualFilledRegions)

                    // 验证恢复后的状态
                    val validationResult = binding.coloringView.validateCurrentState()
                    if (!validationResult.isValid) {
                        Log.w(TAG, "State validation after restore: ${validationResult.message}")
                    }

                    Log.d(TAG, "Progress safely restored for project: $projectName, filled regions: ${actualFilledRegions.size}")

                    // 记录数据一致性检查
                    if (fullProgressData.filledRegions.size != actualFilledRegions.size) {
                        val originalCount = fullProgressData.filledRegions.size
                        val actualCount = actualFilledRegions.size
                        Log.d(TAG, "Progress data filtered: $originalCount -> $actualCount regions")
                    }

                    // 显示恢复成功提示
                    if (fullProgressData.filledRegions.size != getFilledRegions().size) {
                        val filteredCount = fullProgressData.filledRegions.size - getFilledRegions().size
                        android.widget.Toast.makeText(
                            activity,
                            "进度已恢复，过滤了 $filteredCount 个无效区域",
                            android.widget.Toast.LENGTH_SHORT
                        ).show()
                    }
                } else {
                    Log.w(TAG, "Failed to restore progress for project: $projectName")
                    callback?.onProgressRestoreError(projectName, "进度恢复失败")
                    android.widget.Toast.makeText(
                        activity,
                        "进度恢复失败，将从头开始",
                        android.widget.Toast.LENGTH_SHORT
                    ).show()
                }
            }
            is com.example.coloringproject.utils.LoadResult.Error -> {
                Log.d(TAG, "No saved progress found for project: $projectName")
                callback?.onProgressRestoreError(projectName, result.message)
                // 没有保存的进度，这是正常情况，不需要显示错误
            }
        }
    }

    /**
     * 调度预览图片生成（延迟执行，避免频繁生成）
     */
    fun schedulePreviewImageGeneration() {
        // 取消之前的任务
        previewGenerationRunnable?.let { previewGenerationHandler.removeCallbacks(it) }

        // 创建新的延迟任务
        previewGenerationRunnable = Runnable {
            generatePreviewImage()
        }

        // 延迟2秒执行，避免用户连续填色时频繁生成
        previewGenerationHandler.postDelayed(previewGenerationRunnable!!, 2000)

        Log.d(TAG, "已调度预览图片生成任务（2秒后执行）")
    }

    /**
     * 生成预览图片（仅在需要时调用，比如手动保存）
     */
    private fun generatePreviewImage() {
        val coloringData = getCurrentColoringData()
        if (coloringData == null) {
            return
        }

        val projectName = getStandardizedProjectName()

        lifecycleScope.launch(Dispatchers.IO) {
            try {
                // 检查协程是否已被取消
                if (!isActive) {
                    Log.d(TAG, "协程已取消，跳过预览图片生成: $projectName")
                    return@launch
                }

                // 创建预览图片
                val previewBitmap = withContext(Dispatchers.Main) {
                    if (isActive) {
                        binding.coloringView.captureArtwork(false)
                    } else {
                        null
                    }
                }

                if (isActive && previewBitmap != null) {
                    // 生成适合缩略图的尺寸
                    val thumbnailBitmap = createThumbnailBitmap(previewBitmap)
                    projectSaveManager.savePreviewImage(projectName, thumbnailBitmap)

                    // 回收原始bitmap（如果不是同一个对象）
                    if (thumbnailBitmap != previewBitmap) {
                        previewBitmap.recycle()
                    }

                    Log.d(TAG, "Preview image generated for project: $projectName")

                    // 通知Library刷新预览图片
                    val previewPath = projectSaveManager.getPreviewImagePath(projectName)
                    LibraryEventManager.notifyProjectPreviewUpdated(projectName, previewPath)
                    
                    callback?.onPreviewImageGenerated(projectName, previewPath)
                }
            } catch (e: Exception) {
                handleCoroutineException("预览图片生成: $projectName", e, isActive)
            }
        }
    }

    /**
     * 创建适合缩略图显示的bitmap
     */
    private fun createThumbnailBitmap(originalBitmap: Bitmap): Bitmap {
        val maxThumbnailSize = 512 // 最大缩略图尺寸

        val originalWidth = originalBitmap.width
        val originalHeight = originalBitmap.height

        // 如果原图已经足够小，直接返回
        if (originalWidth <= maxThumbnailSize && originalHeight <= maxThumbnailSize) {
            return originalBitmap
        }

        // 计算缩放比例，保持宽高比
        val scale = minOf(
            maxThumbnailSize.toFloat() / originalWidth,
            maxThumbnailSize.toFloat() / originalHeight
        )

        val newWidth = (originalWidth * scale).toInt()
        val newHeight = (originalHeight * scale).toInt()

        // 创建缩放后的bitmap
        val thumbnailBitmap = Bitmap.createScaledBitmap(originalBitmap, newWidth, newHeight, true)

        Log.d(TAG, "创建缩略图: ${originalWidth}x${originalHeight} -> ${newWidth}x${newHeight}")

        return thumbnailBitmap
    }

    /**
     * 安全的协程异常处理
     */
    private fun handleCoroutineException(operationName: String, exception: Exception, isActive: Boolean = true) {
        if (exception is kotlinx.coroutines.CancellationException || exception.cause is kotlinx.coroutines.CancellationException) {
            Log.d(TAG, "协程操作被取消: $operationName")
        } else if (isActive) {
            Log.e(TAG, "协程操作出错: $operationName", exception)
        }
    }

    /**
     * 异步生成预览图片（用于退出时，不阻塞UI）
     */
    fun generatePreviewImageAsync() {
        val coloringData = getCurrentColoringData()
        if (coloringData == null) {
            return
        }

        val projectName = getStandardizedProjectName()

        // 使用GlobalScope确保即使Activity销毁也能完成
        GlobalScope.launch(Dispatchers.IO) {
            try {
                // 创建预览图片
                val previewBitmap = withContext(Dispatchers.Main) {
                    if (!isActivityDestroyed()) {
                        binding.coloringView.captureArtwork(false)
                    } else {
                        null
                    }
                }

                if (previewBitmap != null) {
                    // 生成适合缩略图的尺寸
                    val thumbnailBitmap = createThumbnailBitmap(previewBitmap)
                    projectSaveManager.savePreviewImage(projectName, thumbnailBitmap)

                    // 回收原始bitmap（如果不是同一个对象）
                    if (thumbnailBitmap != previewBitmap) {
                        previewBitmap.recycle()
                    }

                    Log.d(TAG, "Async preview image generated for project: $projectName")

                    // 通知Library刷新预览图片
                    val previewPath = projectSaveManager.getPreviewImagePath(projectName)
                    LibraryEventManager.notifyProjectPreviewUpdated(projectName, previewPath)
                }
            } catch (e: Exception) {
                handleCoroutineException("异步预览图片生成: $projectName", e)
            }
        }
    }

    /**
     * 同步生成预览图片 - 用于退出前确保缩略图更新
     */
    suspend fun generatePreviewImageSync() = withContext(Dispatchers.IO) {
        val projectName = getStandardizedProjectName()
        val coloringData = getCurrentColoringData() ?: return@withContext

        try {
            val startTime = System.currentTimeMillis()

            // 在主线程生成预览图片
            val previewBitmap = withContext(Dispatchers.Main) {
                if (!isActivityDestroyed()) {
                    binding.coloringView.captureArtwork(false)
                } else {
                    null
                }
            }

            val generateTime = System.currentTimeMillis() - startTime
            Log.d(TAG, "同步预览图片生成耗时: ${generateTime}ms")

            if (previewBitmap != null) {
                // 生成适合缩略图的尺寸
                val thumbnailBitmap = createThumbnailBitmap(previewBitmap)
                val saveResult = projectSaveManager.savePreviewImage(projectName, thumbnailBitmap)

                // 回收原始bitmap（如果不是同一个对象）
                if (thumbnailBitmap != previewBitmap) {
                    previewBitmap.recycle()
                }

                Log.d(TAG, "同步预览图片保存完成: $projectName")

                // 在主线程通知Library刷新预览图片
                withContext(Dispatchers.Main) {
                    val previewPath = projectSaveManager.getPreviewImagePath(projectName)
                    LibraryEventManager.notifyProjectPreviewUpdated(projectName, previewPath)
                    callback?.onPreviewImageGenerated(projectName, previewPath)
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "同步预览图片生成失败: $projectName", e)
        }
    }

    /**
     * 同步恢复项目进度 - 用于预加载快速启动
     */
    fun restoreProgressSync(projectName: String) {
        try {
            Log.d(TAG, "=== 开始同步恢复进度 ===")
            Log.d(TAG, "项目名称: $projectName")

            // 使用ProjectSaveManager的正确方法加载进度数据
            val result = projectSaveManager.loadFullProgress(projectName)

            when (result) {
                is com.example.coloringproject.utils.LoadResult.Success -> {
                    val fullProgressData = result.data
                    Log.d(TAG, "找到完整进度数据: ${fullProgressData.filledRegions.size}个填色区域")

                    if (fullProgressData.filledRegions.isNotEmpty()) {
                        // 恢复填色区域
                        val filledRegions = getFilledRegions()
                        filledRegions.clear()
                        filledRegions.addAll(fullProgressData.filledRegions)

                        // 更新ColoringView
                        binding.coloringView.setFilledRegions(filledRegions)

                        callback?.onProgressRestored(projectName, filledRegions.toSet())

                        Log.d(TAG, "同步恢复进度成功: $projectName, 已填色区域: ${filledRegions.size}")
                    } else {
                        Log.d(TAG, "进度数据存在但无填色区域: $projectName")
                    }
                }
                is com.example.coloringproject.utils.LoadResult.Error -> {
                    Log.d(TAG, "无进度数据: $projectName - ${result.message}")
                    callback?.onProgressRestoreError(projectName, result.message)

                    // 列出保存目录下的所有文件，帮助调试
                    val saveDir = java.io.File(activity.filesDir, "coloring_saves")
                    if (saveDir.exists()) {
                        val files = saveDir.listFiles()
                        Log.d(TAG, "coloring_saves目录下的文件: ${files?.map { it.name }}")
                    } else {
                        Log.d(TAG, "coloring_saves目录不存在")
                    }
                }
            }

        } catch (e: Exception) {
            Log.e(TAG, "同步恢复进度失败: $projectName", e)
            callback?.onProgressRestoreError(projectName, e.message ?: "未知错误")
        }
    }

    override fun cleanup() {
        super.cleanup()
        // 清理预览图片生成任务
        previewGenerationRunnable?.let { previewGenerationHandler.removeCallbacks(it) }
    }
}
