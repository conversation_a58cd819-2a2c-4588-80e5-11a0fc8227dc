package com.example.coloringproject.manager

import android.os.Handler
import android.os.Looper
import android.util.Log
import com.example.coloringproject.SimpleMainActivity
import com.example.coloringproject.databinding.ActivitySimpleMainBinding

/**
 * 自动演示管理器
 * 负责自动演示功能
 */
class AutoDemoManager(
    activity: SimpleMainActivity,
    binding: ActivitySimpleMainBinding
) : BaseManager(activity, binding) {

    companion object {
        private const val TAG = "AutoDemoManager"
    }

    // 自动演示相关
    private var isAutoDemo = false
    private val autoHandler = Handler(Looper.getMainLooper())
    private var autoRunnable: Runnable? = null
    private var currentRegionIndex = 0

    private var colorManager: ColorManager? = null
    private var progressManager: ProgressManager? = null

    /**
     * 设置依赖的管理器
     */
    fun setDependencies(colorManager: ColorManager, progressManager: ProgressManager) {
        this.colorManager = colorManager
        this.progressManager = progressManager
    }

    /**
     * 切换自动演示模式
     */
    fun toggleAutoDemo() {
        Log.d(TAG, "toggleAutoDemo called, isAutoDemo=$isAutoDemo")
        if (isAutoDemo) {
            Log.d(TAG, "停止演示")
            stopAutoDemo()
        } else {
            Log.d(TAG, "开始演示")
            startAutoDemo()
        }
    }

    /**
     * 开始自动演示
     */
    fun startAutoDemo() {
        Log.d(TAG, "startAutoDemo called")
        val coloringData = getCurrentColoringData()
        if (coloringData == null) {
            Log.w(TAG, "currentColoringData is null")
            return
        }

        Log.d(TAG, "项目数据已加载，区域数: ${coloringData.regions.size}")

        isAutoDemo = true
        currentRegionIndex = 0

        // 重置填色状态
        getFilledRegions().clear()
        binding.coloringView.resetColoring()
        updateProgress()

        // 设置为适合屏幕的大小，不进行自动跟踪
        binding.coloringView.zoomToFit()

        val message = "开始自动演示 - 每0.2秒填充一个区域 (共${coloringData.regions.size}个区域)"
        Log.d(TAG, "开始自动演示，总共 ${coloringData.regions.size} 个区域")

        // 开始自动填色
        scheduleNextAutoFill()
    }

    /**
     * 停止自动演示
     */
    fun stopAutoDemo() {
        isAutoDemo = false

        // 取消定时任务
        autoRunnable?.let { autoHandler.removeCallbacks(it) }
        autoRunnable = null

        Log.d(TAG, "自动演示已停止")
    }

    /**
     * 安排下一次自动填色
     */
    private fun scheduleNextAutoFill() {
        if (!isAutoDemo) return

        val coloringData = getCurrentColoringData() ?: return

        // 检查是否还有区域需要填色
        if (currentRegionIndex >= coloringData.regions.size) {
            // 所有区域都已填色，演示完成
            completeAutoDemo()
            return
        }

        autoRunnable = Runnable {
            performAutoFill()
        }

        // 0.01秒后执行下一次填色（快速演示）
        autoHandler.postDelayed(autoRunnable!!, 10)
    }

    /**
     * 执行自动填色
     */
    private fun performAutoFill() {
        if (!isAutoDemo) {
            Log.d(TAG, "演示已停止，跳过填色")
            return
        }

        val coloringData = getCurrentColoringData() ?: return

        if (currentRegionIndex >= coloringData.regions.size) {
            Log.d(TAG, "所有区域已完成，结束演示")
            completeAutoDemo()
            return
        }

        val region = coloringData.regions[currentRegionIndex]

        Log.d(TAG, "自动填色区域 ${region.id} (${currentRegionIndex + 1}/${coloringData.regions.size})")

        // 找到对应的调色板颜色
        val paletteColor = coloringData.colorPalette.find {
            it.colorHex.equals(region.colorHex, ignoreCase = true)
        }

        if (paletteColor != null) {
            Log.d(TAG, "找到匹配颜色: ${paletteColor.name} (${paletteColor.colorHex})")

            // 选择对应的颜色
            colorManager?.selectColor(paletteColor)

            // 模拟填色
            getFilledRegions().add(region.id)
            binding.coloringView.addFilledRegion(region.id)

            // 更新进度
            updateProgress()

            // 每10个区域显示一次进度（避免Toast过多）
            if (currentRegionIndex % 10 == 0 || currentRegionIndex == coloringData.regions.size - 1) {
                val progressText = "${currentRegionIndex + 1}/${coloringData.regions.size}"
                Log.d(TAG, "填色进度: $progressText")
            }
        } else {
            Log.w(TAG, "未找到区域 ${region.id} 的匹配颜色: ${region.colorHex}")
        }

        currentRegionIndex++

        // 安排下一次填色
        scheduleNextAutoFill()
    }

    /**
     * 完成自动演示
     */
    private fun completeAutoDemo() {
        isAutoDemo = false

        Log.d(TAG, "自动演示完成")

        // 显示完成对话框
        showCompletionDialog()
    }

    /**
     * 更新进度显示
     */
    private fun updateProgress() {
        val coloringData = getCurrentColoringData() ?: return
        val totalRegions = coloringData.metadata.totalRegions
        val filled = getFilledRegions().size
        
        // 这里应该通过回调更新UI，而不是直接操作UI
        Log.d(TAG, "Progress: $filled/$totalRegions")
    }

    /**
     * 显示完成对话框
     */
    private fun showCompletionDialog() {
        // 这里应该通过回调通知Activity显示完成对话框
        Log.d(TAG, "自动演示完成，显示完成对话框")
    }

    /**
     * 检查是否正在自动演示
     */
    fun isAutoDemoRunning(): Boolean = isAutoDemo

    /**
     * 获取当前演示进度
     */
    fun getDemoProgress(): Pair<Int, Int> {
        val coloringData = getCurrentColoringData()
        return if (coloringData != null) {
            Pair(currentRegionIndex, coloringData.regions.size)
        } else {
            Pair(0, 0)
        }
    }

    override fun cleanup() {
        super.cleanup()
        // 停止自动演示
        stopAutoDemo()
    }
}

/**
 * 图片保存管理器
 * 负责图片保存功能
 */
class ImageSaveManager(
    activity: SimpleMainActivity,
    binding: ActivitySimpleMainBinding
) : BaseManager(activity, binding) {

    companion object {
        private const val TAG = "ImageSaveManager"
    }

    /**
     * 保存当前图片
     */
    fun saveCurrentImage() {
        saveCurrentProgress()
    }

    /**
     * 保存画作到相册
     */
    fun saveArtworkToGallery() {
        // 检查权限
        if (!com.example.coloringproject.utils.PermissionHelper.hasStoragePermission(activity)) {
            if (com.example.coloringproject.utils.PermissionHelper.shouldShowPermissionRationale(activity)) {
                // 显示权限说明
                androidx.appcompat.app.AlertDialog.Builder(activity)
                    .setTitle("需要存储权限")
                    .setMessage(com.example.coloringproject.utils.PermissionHelper.getPermissionRationaleMessage())
                    .setPositiveButton("授权") { _, _ ->
                        com.example.coloringproject.utils.PermissionHelper.requestStoragePermission(activity)
                    }
                    .setNegativeButton("取消", null)
                    .show()
            } else {
                com.example.coloringproject.utils.PermissionHelper.requestStoragePermission(activity)
            }
            return
        }

        // 显示保存选项对话框
        showSaveOptionsDialog()
    }

    /**
     * 显示保存选项对话框
     */
    private fun showSaveOptionsDialog() {
        val options = arrayOf(
            "保存填色画作",
            "保存对比图（原图+填色图）",
            "保存完整作品集"
        )

        androidx.appcompat.app.AlertDialog.Builder(activity)
            .setTitle("选择保存内容")
            .setItems(options) { _, which ->
                when (which) {
                    0 -> saveFilledArtwork()
                    1 -> saveComparisonArtwork()
                    2 -> saveArtworkCollection()
                }
            }
            .show()
    }

    /**
     * 保存填色画作
     */
    private fun saveFilledArtwork() {
        val artwork = binding.coloringView.captureArtwork(false)
        if (artwork != null) {
            lifecycleScope.launch {
                val success = com.example.coloringproject.utils.ImageSaver.saveImageToGallery(
                    activity,
                    artwork,
                    "coloring_artwork_${System.currentTimeMillis()}.jpg"
                )
                com.example.coloringproject.utils.ImageSaver.showSaveResult(activity, success)
            }
        }
    }

    /**
     * 保存对比画作
     */
    private fun saveComparisonArtwork() {
        val artwork = binding.coloringView.captureArtwork(true)
        if (artwork != null) {
            lifecycleScope.launch {
                val success = com.example.coloringproject.utils.ImageSaver.saveImageToGallery(
                    activity,
                    artwork,
                    "coloring_comparison_${System.currentTimeMillis()}.jpg"
                )
                com.example.coloringproject.utils.ImageSaver.showSaveResult(activity, success)
            }
        }
    }

    /**
     * 保存完整作品集
     */
    private fun saveArtworkCollection() {
        val filledArtwork = binding.coloringView.captureArtwork(false)
        if (filledArtwork != null) {
            lifecycleScope.launch {
                val projectName = getStandardizedProjectName()

                val result = com.example.coloringproject.utils.ImageSaver.saveArtworkCollection(
                    activity,
                    filledArtwork,
                    null, // 可以传入原图bitmap
                    projectName
                )
            }
        }
    }

    /**
     * 保存当前进度
     */
    private fun saveCurrentProgress() {
        // 这里应该委托给ProgressManager处理
        Log.d(TAG, "保存当前进度")
    }
}
