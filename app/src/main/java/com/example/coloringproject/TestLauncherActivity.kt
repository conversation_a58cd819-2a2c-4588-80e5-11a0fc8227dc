package com.example.coloringproject

import android.content.Intent
import android.os.Bundle
import android.view.View
import android.widget.Button
import android.widget.LinearLayout
import android.widget.TextView
import androidx.appcompat.app.AppCompatActivity

class TestLauncherActivity : AppCompatActivity() {
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(createLayout())
    }
    
    private fun createLayout(): View {
        val layout = LinearLayout(this).apply {
            orientation = LinearLayout.VERTICAL
            setPadding(32, 32, 32, 32)
        }
        
        // 标题
        val title = TextView(this).apply {
            text = "测试中心"
            textSize = 28f
            setPadding(0, 0, 0, 48)
        }
        layout.addView(title)
        
        // 描述
        val description = TextView(this).apply {
            text = "选择要运行的测试类型："
            textSize = 16f
            setPadding(0, 0, 0, 32)
        }
        layout.addView(description)
        
        // 综合测试按钮
        val btnComprehensiveTest = Button(this).apply {
            text = "🔍 综合测试套件"
            textSize = 18f
            setPadding(0, 24, 0, 24)
            setOnClickListener {
                startActivity(Intent(this@TestLauncherActivity, TestRunnerActivity::class.java))
            }
        }
        layout.addView(btnComprehensiveTest)
        
        // 性能基准测试按钮
        val btnBenchmark = Button(this).apply {
            text = "⚡ 性能基准测试"
            textSize = 18f
            setPadding(0, 24, 0, 24)
            setOnClickListener {
                startActivity(Intent(this@TestLauncherActivity, BenchmarkActivity::class.java))
            }
        }
        layout.addView(btnBenchmark)
        
        // 内存分析按钮
        val btnMemoryAnalysis = Button(this).apply {
            text = "🧠 内存分析"
            textSize = 18f
            setPadding(0, 24, 0, 24)
            setOnClickListener {
                startActivity(Intent(this@TestLauncherActivity, MemoryAnalysisActivity::class.java))
            }
        }
        layout.addView(btnMemoryAnalysis)

        // 网络功能测试按钮
        val btnNetworkTest = Button(this).apply {
            text = "🌐 网络功能测试"
            textSize = 18f
            setPadding(0, 24, 0, 24)
            setOnClickListener {
                startActivity(Intent(this@TestLauncherActivity, NetworkTestActivity::class.java))
            }
        }
        layout.addView(btnNetworkTest)

        // ProjectType测试按钮
        val btnProjectTypeTest = Button(this).apply {
            text = "🏷️ ProjectType测试"
            textSize = 18f
            setPadding(0, 24, 0, 24)
            setOnClickListener {
                startActivity(Intent(this@TestLauncherActivity, ProjectTypeTestActivity::class.java))
            }
        }
        layout.addView(btnProjectTypeTest)

        // 服务器测试按钮
        val btnServerTest = Button(this).apply {
            text = "🖥️ 服务器测试 (8083)"
            textSize = 18f
            setPadding(0, 24, 0, 24)
            setOnClickListener {
                startActivity(Intent(this@TestLauncherActivity, ServerTestActivity::class.java))
            }
        }
        layout.addView(btnServerTest)

        // 混合数据测试按钮
        val btnHybridTest = Button(this).apply {
            text = "🔄 混合数据测试"
            textSize = 18f
            setPadding(0, 24, 0, 24)
            setOnClickListener {
                // startActivity(Intent(this@TestLauncherActivity, HybridDataTestActivity::class.java)) // 已删除
            }
        }
        layout.addView(btnHybridTest)
        
        // 返回主应用按钮
        val btnBackToMain = Button(this).apply {
            text = "🏠 返回主应用"
            textSize = 18f
            setPadding(0, 24, 0, 24)
            setOnClickListener {
                startActivity(Intent(this@TestLauncherActivity, SplashActivity::class.java))
                finish()
            }
        }
        layout.addView(btnBackToMain)
        
        return layout
    }
}