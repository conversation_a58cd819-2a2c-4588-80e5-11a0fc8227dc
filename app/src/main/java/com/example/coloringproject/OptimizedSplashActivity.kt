package com.example.coloringproject

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.util.Log
import android.view.View
import android.widget.ProgressBar
import android.widget.TextView
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.lifecycleScope
import com.example.coloringproject.config.NetworkConfig
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

/**
 * 优化的启动页面
 * 实现快速启动，最小化预加载，智能导航
 */
class OptimizedSplashActivity : AppCompatActivity() {
    
    companion object {
        private const val TAG = "OptimizedSplashActivity"
        private const val MIN_SPLASH_TIME = 1500L // 最小显示时间1.5秒
        private const val FIRST_LAUNCH_KEY = "is_first_launch"
    }
    
    private lateinit var progressBar: ProgressBar
    private lateinit var statusText: TextView
    private lateinit var versionText: TextView
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        Log.d(TAG, "🚀 启动页面开始")
        
        // 1. 快速显示启动画面
        setupSplashScreen()
        
        // 2. 最小化初始化检查
        lifecycleScope.launch {
            performMinimalInitialization()
        }
    }
    
    /**
     * 设置启动画面
     */
    private fun setupSplashScreen() {
        setContentView(R.layout.activity_optimized_splash)
        
        // 隐藏状态栏和导航栏，全屏显示
        window.decorView.systemUiVisibility = (
            View.SYSTEM_UI_FLAG_FULLSCREEN or
            View.SYSTEM_UI_FLAG_HIDE_NAVIGATION or
            View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY
        )
        
        // 初始化UI组件
        progressBar = findViewById(R.id.splashProgressBar)
        statusText = findViewById(R.id.splashStatusText)
        versionText = findViewById(R.id.splashVersionText)
        
        // 设置版本信息
        try {
            val packageInfo = packageManager.getPackageInfo(packageName, 0)
            versionText.text = "版本 ${packageInfo.versionName}"
        } catch (e: Exception) {
            versionText.text = "版本 1.0"
        }
        
        Log.d(TAG, "✅ 启动画面设置完成")
    }
    
    /**
     * 执行最小化初始化
     */
    private suspend fun performMinimalInitialization() {
        val startTime = System.currentTimeMillis()
        
        try {
            // 阶段1：基础检查
            updateProgress(20, "检查应用状态...")
            delay(200)
            
            // 检查网络功能状态
            val networkEnabled = NetworkConfig.isNetworkFeatureEnabled()
            Log.d(TAG, "网络功能状态: ${if (networkEnabled) "启用" else "禁用"}")
            
            // 阶段2：检查首次启动
            updateProgress(40, "准备用户界面...")
            delay(200)
            
            val isFirstLaunch = checkFirstLaunch()
            
            // 阶段3：准备导航
            updateProgress(60, "正在准备...")
            delay(200)
            
            // 阶段4：完成准备
            updateProgress(80, "即将完成...")
            delay(200)
            
            updateProgress(100, "完成!")
            delay(300)
            
            // 确保最小显示时间
            val elapsedTime = System.currentTimeMillis() - startTime
            if (elapsedTime < MIN_SPLASH_TIME) {
                delay(MIN_SPLASH_TIME - elapsedTime)
            }
            
            // 导航到主界面
            navigateToMain(isFirstLaunch)
            
        } catch (e: Exception) {
            Log.e(TAG, "初始化失败", e)
            updateProgress(0, "启动失败，请重试")
            delay(2000)
            finish()
        }
    }
    
    /**
     * 检查是否首次启动
     */
    private suspend fun checkFirstLaunch(): Boolean {
        return try {
            val prefs = getSharedPreferences("app_prefs", Context.MODE_PRIVATE)
            val isFirstLaunch = !prefs.getBoolean(FIRST_LAUNCH_KEY, false)
            
            if (isFirstLaunch) {
                // 标记为已启动过
                prefs.edit().putBoolean(FIRST_LAUNCH_KEY, true).apply()
                Log.d(TAG, "🎉 首次启动")
            } else {
                Log.d(TAG, "🔄 非首次启动")
            }
            
            isFirstLaunch
        } catch (e: Exception) {
            Log.e(TAG, "检查首次启动失败", e)
            false
        }
    }
    
    /**
     * 导航到主界面
     */
    private fun navigateToMain(isFirstLaunch: Boolean) {
        try {
            val intent = if (isFirstLaunch) {
                // 首次启动，可以显示欢迎界面或教程
                Log.d(TAG, "🎯 导航到欢迎界面")
                Intent(this, EnhancedMainActivity::class.java).apply {
                    putExtra("show_welcome", true)
                }
            } else {
                // 正常启动，直接进入主界面
                Log.d(TAG, "🎯 导航到主界面")
                Intent(this, EnhancedMainActivity::class.java)
            }
            
            startActivity(intent)
            
            // 使用淡入淡出动画
            overridePendingTransition(android.R.anim.fade_in, android.R.anim.fade_out)
            
            finish()
            
        } catch (e: Exception) {
            Log.e(TAG, "导航失败", e)
            finish()
        }
    }
    
    /**
     * 更新进度显示
     */
    private suspend fun updateProgress(progress: Int, status: String) {
        try {
            progressBar.progress = progress
            statusText.text = status
            Log.d(TAG, "📊 进度: $progress% - $status")
        } catch (e: Exception) {
            Log.e(TAG, "更新进度失败", e)
        }
    }
    
    override fun onBackPressed() {
        // 启动页面禁用返回键
        // 不调用 super.onBackPressed()
    }
    
    override fun onDestroy() {
        super.onDestroy()
        Log.d(TAG, "🔚 启动页面销毁")
    }
}
