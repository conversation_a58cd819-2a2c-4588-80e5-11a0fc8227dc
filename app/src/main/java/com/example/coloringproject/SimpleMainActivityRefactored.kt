package com.example.coloringproject

import android.graphics.Bitmap
import android.os.Bundle
import android.util.Log
import android.view.Menu
import android.view.MenuItem
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import com.example.coloringproject.adapter.ImmersiveColorAdapter
import com.example.coloringproject.callback.*
import com.example.coloringproject.data.ColoringData
import com.example.coloringproject.data.ColorPalette
import com.example.coloringproject.data.TouchResult
import com.example.coloringproject.databinding.ActivitySimpleMainBinding
import com.example.coloringproject.factory.ManagerFactory
import com.example.coloringproject.factory.ManagerCollection
import com.example.coloringproject.manager.ProjectPreloadManager
import com.example.coloringproject.model.ColorInfo
import com.example.coloringproject.utils.EnhancedAssetManager
import com.example.coloringproject.utils.ProjectNameUtils
import com.example.coloringproject.viewmodel.ColoringViewModel
import kotlinx.coroutines.launch

/**
 * 重构后的 SimpleMainActivity
 * 使用管理器模式，大大简化了主Activity的代码
 */
class SimpleMainActivityRefactored : AppCompatActivity() {

    companion object {
        private const val TAG = "SimpleMainActivityRefactored"
    }

    // ViewBinding
    private lateinit var binding: ActivitySimpleMainBinding

    // 管理器集合
    private lateinit var managers: ManagerCollection

    // 沉浸式界面相关
    private var hintCount = 10000
    private lateinit var immersiveColorAdapter: ImmersiveColorAdapter
    private val viewModel: ColoringViewModel by viewModels()

    // 数据状态
    private var currentColoringData: ColoringData? = null
    private val filledRegions = mutableSetOf<Int>()
    private var hintsEnabled = true

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        binding = ActivitySimpleMainBinding.inflate(layoutInflater)
        setContentView(binding.root)
        Log.d(TAG, "onCreate started")

        // 初始化管理器
        initializeManagers()

        // 设置共享元素过渡
        setupSharedElementTransition()

        // 显示网络功能状态
        Log.i(TAG, com.example.coloringproject.config.NetworkConfig.getFeatureStatusReport())

        // 初始化UI
        initViews()
        setupUI()

        // 处理Intent参数并加载项目
        handleIntentAndLoadProject()
        
        Log.d(TAG, "onCreate completed")
    }

    /**
     * 初始化所有管理器
     */
    private fun initializeManagers() {
        val factory = ManagerFactory(this, binding)
        managers = factory.createAllManagers()

        // 设置管理器回调
        setupManagerCallbacks()

        // 初始化所有管理器
        managers.initializeAll()

        Log.d(TAG, "所有管理器初始化完成")
    }

    /**
     * 设置管理器回调
     */
    private fun setupManagerCallbacks() {
        // 项目加载回调
        managers.projectLoader.setCallback(object : ProjectLoadCallback {
            override fun onProjectLoaded(coloringData: ColoringData, outlineBitmap: Bitmap) {
                managers.projectSetupManager.setupProject(coloringData, outlineBitmap)
            }

            override fun onProjectLoadError(title: String, message: String) {
                managers.uiStateManager.showError("$title: $message")
            }

            override fun onShowProjectSelector(projects: List<EnhancedAssetManager.ValidatedProject>) {
                // 显示项目选择器的逻辑
                showProjectSelector(projects)
            }

            override fun onLoadingStarted() {
                managers.uiStateManager.showLoading()
            }

            override fun onLoadingCompleted() {
                managers.uiStateManager.showReady()
            }
        })

        // 颜色变化回调
        managers.colorManager.setCallback(object : ColorChangeCallback {
            override fun onColorSelected(colorHex: String) {
                // 颜色选择后的处理
                updateProgress()
            }

            override fun onColorCompleted(colorHex: String, regionCount: Int) {
                // 某种颜色完成时的处理
                managers.progressManager.schedulePreviewImageGeneration()
            }

            override fun onProjectCompleted() {
                // 整个项目完成时的处理
                showCompletionDialog()
            }

            override fun onColorProgressUpdated(colorIndex: Int, progress: Int) {
                // 颜色进度更新
            }
        })

        // 进度管理回调
        managers.progressManager.setCallback(object : ProgressCallback {
            override fun onProgressSaved(projectName: String) {
                Log.d(TAG, "进度保存成功: $projectName")
            }

            override fun onProgressSaveError(projectName: String, error: String) {
                Log.e(TAG, "进度保存失败: $projectName - $error")
            }

            override fun onProgressRestored(projectName: String, filledRegions: Set<Int>) {
                updateFilledRegions(filledRegions)
                updateProgress()
                managers.colorManager.updateAllColorProgress()
            }

            override fun onProgressRestoreError(projectName: String, error: String) {
                Log.w(TAG, "进度恢复失败: $projectName - $error")
            }

            override fun onPreviewImageGenerated(projectName: String, previewPath: String?) {
                Log.d(TAG, "预览图生成完成: $projectName")
            }
        })

        // UI状态回调
        managers.uiStateManager.setCallback(object : UIStateCallback {
            override fun onShowLoading() {
                // 加载状态UI处理
            }

            override fun onShowReady() {
                // 就绪状态UI处理
            }

            override fun onShowError(message: String) {
                // 错误状态UI处理
            }

            override fun onShowColoringUI() {
                // 显示填色UI
            }

            override fun onHideColoringUI() {
                // 隐藏填色UI
            }
        })
    }

    /**
     * 初始化视图
     */
    private fun initViews() {
        with(binding) {
            // 设置返回按钮
            btnBack.setOnClickListener {
                onBackPressed()
            }

            // 设置商店按钮
            btnShop.setOnClickListener {
                // TODO: 打开商店页面
            }

            // 设置涂色提醒按钮
            btnHint.setOnClickListener {
                if (hintCount > 0) {
                    useHint()
                }
            }

            // 设置缩放控制
            btnZoomIn.setOnClickListener {
                coloringView.zoomIn()
            }

            btnZoomOut.setOnClickListener {
                coloringView.zoomOut()
            }

            btnZoomFit.setOnClickListener {
                coloringView.zoomToFit()
            }

            // 设置测试功能按钮
            var isTestButtonsVisible = false
            btnHint.setOnLongClickListener {
                isTestButtonsVisible = !isTestButtonsVisible
                testButtonsGroup.visibility = if (isTestButtonsVisible) android.view.View.VISIBLE else android.view.View.GONE
                true
            }

            // 更新提醒次数显示
            managers.uiStateManager.updateHintCountDisplay(hintCount)
        }
    }

    /**
     * 设置UI
     */
    private fun setupUI() {
        // 设置沉浸式调色板
        immersiveColorAdapter = ImmersiveColorAdapter(emptyList()) { colorInfo ->
            Log.d(TAG, "选择颜色: ${colorInfo.name}")
            // 转换为ColorPalette格式并选择颜色
            val colorPalette = convertColorInfoToPalette(colorInfo)
            managers.colorManager.selectColor(colorPalette)
        }

        // 设置颜色管理器的适配器
        managers.colorManager.setImmersiveColorAdapter(immersiveColorAdapter)

        binding.recyclerViewColors.apply {
            layoutManager = LinearLayoutManager(this@SimpleMainActivityRefactored, LinearLayoutManager.HORIZONTAL, false)
            adapter = immersiveColorAdapter
        }

        // 设置填色View事件
        binding.coloringView.onRegionTouched = { result ->
            handleRegionTouch(result)
        }

        binding.coloringView.onProjectCompleted = {
            managers.colorManager.getCurrentSelectedColor()?.let {
                managers.colorManager.setCallback(null)?.onProjectCompleted()
            }
        }

        binding.coloringView.onColorCompleted = { colorHex, regionCount ->
            managers.colorManager.setCallback(null)?.onColorCompleted(colorHex, regionCount)
        }

        binding.coloringView.onColorAutoSelected = { colorHex ->
            runOnUiThread {
                managers.colorManager.autoSelectColorByHex(colorHex)
            }
        }

        // 设置提醒状态
        hintsEnabled = true
        binding.coloringView.setShowHints(hintsEnabled)
        binding.coloringView.setHintAlpha(0.4f)

        // 初始状态
        managers.uiStateManager.showLoading()
    }

    /**
     * 处理Intent参数并加载项目
     */
    private fun handleIntentAndLoadProject() {
        val projectId = intent.getStringExtra("project_id")
        val projectName = intent.getStringExtra("project_name")
        val projectSource = intent.getStringExtra("project_source")
        val fromGallery = intent.getBooleanExtra("from_gallery", false)
        val hasProgress = intent.getBooleanExtra("has_progress", false)
        val hasPreloadedData = intent.getBooleanExtra("has_preloaded_data", false)

        Log.d(TAG, "Intent参数: projectId=$projectId, projectName=$projectName, projectSource=$projectSource")

        when {
            projectId != null -> {
                if (hasPreloadedData) {
                    val preloadManager = ProjectPreloadManager.getInstance(this)
                    val preloadedData = preloadManager.getPreloadedProject(projectId)
                    if (preloadedData != null) {
                        Log.d(TAG, "使用预加载数据快速启动: $projectId")
                        managers.projectSetupManager.initializeWithPreloadedData(preloadedData)
                        return
                    }
                }
                managers.projectLoader.loadProjectById(projectId, projectSource)
            }
            projectName != null -> {
                if (fromGallery) {
                    managers.projectLoader.loadProjectFromGallery(projectName, hasProgress)
                } else {
                    managers.projectLoader.loadSpecificProject(projectName)
                }
            }
            else -> {
                managers.projectLoader.loadFirstValidatedProject()
            }
        }
    }

    /**
     * 处理区域触摸
     */
    private fun handleRegionTouch(result: TouchResult) {
        if (result.isValidTouch && result.regionId != null) {
            filledRegions.add(result.regionId)
            updateProgress()
            
            // 使用颜色管理器更新进度
            managers.colorManager.updateCurrentSelectedColorProgress()
            
            // 保存进度
            managers.progressManager.saveCurrentProgress()
        }
    }

    /**
     * 更新进度显示
     */
    private fun updateProgress() {
        val totalRegions = currentColoringData?.metadata?.totalRegions ?: 0
        val filled = filledRegions.size
        managers.uiStateManager.updateProgressDisplay(filled, totalRegions)
    }

    /**
     * 使用涂色提醒
     */
    private fun useHint() {
        if (hintCount > 0) {
            hintCount--
            managers.uiStateManager.updateHintCountDisplay(hintCount)
            binding.coloringView.showNextColorHint()
        }
    }

    // 供管理器访问的方法
    fun getCurrentColoringData(): ColoringData? = currentColoringData
    fun getFilledRegions(): MutableSet<Int> = filledRegions
    fun updateFilledRegions(regions: Set<Int>) {
        filledRegions.clear()
        filledRegions.addAll(regions)
    }
    fun getStandardizedProjectName(): String = ProjectNameUtils.getUnifiedProjectId(intent = intent)

    // 其他辅助方法...
    private fun convertColorInfoToPalette(colorInfo: ColorInfo): ColorPalette {
        // 实现ColorInfo到ColorPalette的转换
        return ColorPalette(
            id = colorInfo.id,
            colorHex = colorInfo.hexColor,
            colorRgb = listOf(0, 0, 0), // 简化实现
            name = colorInfo.name,
            usageCount = 0
        )
    }

    private fun showProjectSelector(projects: List<EnhancedAssetManager.ValidatedProject>) {
        // 实现项目选择器显示逻辑
    }

    private fun showCompletionDialog() {
        // 实现完成对话框显示逻辑
    }

    private fun setupSharedElementTransition() {
        // 实现共享元素过渡设置
    }

    override fun onCreateOptionsMenu(menu: Menu?): Boolean {
        menuInflater.inflate(R.menu.main_menu, menu)
        return true
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        return when (item.itemId) {
            R.id.action_select_project -> {
                managers.projectLoader.loadFirstValidatedProject()
                true
            }
            R.id.action_save_image -> {
                managers.imageSaveManager.saveCurrentImage()
                true
            }
            R.id.action_reset -> {
                resetProject()
                true
            }
            else -> super.onOptionsItemSelected(item)
        }
    }

    private fun resetProject() {
        filledRegions.clear()
        binding.coloringView.resetColoring()
        updateProgress()
        managers.colorManager.updateAllColorProgress()
    }

    override fun onBackPressed() {
        if (currentColoringData != null && filledRegions.isNotEmpty()) {
            lifecycleScope.launch {
                managers.progressManager.saveCurrentProgressSync()
                managers.progressManager.generatePreviewImageSync()
                super.onBackPressed()
            }
        } else {
            super.onBackPressed()
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        // 清理所有管理器
        managers.cleanupAll()
        Log.d(TAG, "SimpleMainActivityRefactored destroyed and resources cleaned up")
    }
}
