package com.example.coloringproject.utils

import android.app.ActivityManager
import android.content.ComponentCallbacks2
import android.content.Context
import android.content.res.Configuration
import android.os.Debug
import android.util.Log
import kotlinx.coroutines.*
import java.lang.ref.WeakReference
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.atomic.AtomicBoolean
import java.util.concurrent.atomic.AtomicLong

/**
 * 内存压力感知管理器
 * 监控系统内存压力，智能释放缓存和资源
 */
class MemoryPressureManager private constructor(private val context: Context) : ComponentCallbacks2 {
    
    companion object {
        private const val TAG = "MemoryPressureManager"
        
        // 内存压力阈值
        private const val MEMORY_PRESSURE_THRESHOLD_HIGH = 0.85f // 85%内存使用率
        private const val MEMORY_PRESSURE_THRESHOLD_CRITICAL = 0.95f // 95%内存使用率
        
        // 监控间隔
        private const val MEMORY_MONITOR_INTERVAL = 30000L // 30秒
        private const val MEMORY_CHECK_INTERVAL = 5000L // 5秒
        
        @Volatile
        private var INSTANCE: MemoryPressureManager? = null
        
        fun getInstance(context: Context): MemoryPressureManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: MemoryPressureManager(context.applicationContext).also { INSTANCE = it }
            }
        }
    }
    
    private val activityManager = context.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
    private val monitorScope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    
    // 内存监控状态
    private val isMonitoring = AtomicBoolean(false)
    private val lastMemoryCheck = AtomicLong(0)
    private val memoryPressureLevel = AtomicLong(ComponentCallbacks2.TRIM_MEMORY_COMPLETE.toLong())
    
    // 缓存管理器引用
    private val cacheManagers = ConcurrentHashMap<String, WeakReference<CacheManager>>()
    
    // 内存统计
    private var lastMemoryInfo: MemoryInfo? = null
    
    /**
     * 内存信息数据类
     */
    data class MemoryInfo(
        val totalMemory: Long,
        val availableMemory: Long,
        val usedMemory: Long,
        val usagePercentage: Float,
        val isLowMemory: Boolean,
        val timestamp: Long = System.currentTimeMillis()
    )
    
    /**
     * 内存压力级别
     */
    enum class MemoryPressureLevel(val priority: Int, val description: String) {
        NORMAL(0, "正常"),
        MODERATE(1, "轻度压力"),
        HIGH(2, "高压力"),
        CRITICAL(3, "严重压力")
    }
    
    /**
     * 缓存管理器接口
     */
    interface CacheManager {
        fun clearLowPriorityCache()
        fun clearMediumPriorityCache()
        fun clearHighPriorityCache()
        fun clearAllCache()
        fun getCacheSize(): Long
        fun getCacheName(): String
    }
    
    /**
     * 开始内存监控
     */
    fun startMemoryMonitoring() {
        if (isMonitoring.compareAndSet(false, true)) {
            Log.d(TAG, "🔍 开始内存压力监控")
            
            // 注册系统内存回调
            context.registerComponentCallbacks(this)
            
            // 启动定期内存检查
            monitorScope.launch {
                while (isMonitoring.get()) {
                    try {
                        checkMemoryPressure()
                        delay(MEMORY_CHECK_INTERVAL)
                    } catch (e: Exception) {
                        Log.e(TAG, "内存检查异常", e)
                        delay(MEMORY_CHECK_INTERVAL * 2) // 异常时延长间隔
                    }
                }
            }
            
            // 启动详细内存监控
            monitorScope.launch {
                while (isMonitoring.get()) {
                    try {
                        logDetailedMemoryInfo()
                        delay(MEMORY_MONITOR_INTERVAL)
                    } catch (e: Exception) {
                        Log.e(TAG, "详细内存监控异常", e)
                    }
                }
            }
        }
    }
    
    /**
     * 停止内存监控
     */
    fun stopMemoryMonitoring() {
        if (isMonitoring.compareAndSet(true, false)) {
            Log.d(TAG, "🛑 停止内存压力监控")
            
            try {
                context.unregisterComponentCallbacks(this)
            } catch (e: Exception) {
                Log.e(TAG, "取消注册内存回调失败", e)
            }
            
            monitorScope.cancel()
        }
    }
    
    /**
     * 注册缓存管理器
     */
    fun registerCacheManager(name: String, cacheManager: CacheManager) {
        cacheManagers[name] = WeakReference(cacheManager)
        Log.d(TAG, "注册缓存管理器: $name")
    }
    
    /**
     * 取消注册缓存管理器
     */
    fun unregisterCacheManager(name: String) {
        cacheManagers.remove(name)
        Log.d(TAG, "取消注册缓存管理器: $name")
    }
    
    /**
     * 检查内存压力
     */
    private suspend fun checkMemoryPressure() = withContext(Dispatchers.IO) {
        val memoryInfo = getCurrentMemoryInfo()
        lastMemoryInfo = memoryInfo
        
        val pressureLevel = determinePressureLevel(memoryInfo)
        
        // 如果内存压力发生变化，执行相应的缓存清理
        if (shouldTriggerCacheCleanup(pressureLevel, memoryInfo)) {
            Log.w(TAG, "⚠️ 检测到内存压力: ${pressureLevel.description} (使用率: ${(memoryInfo.usagePercentage * 100).toInt()}%)")
            handleMemoryPressure(pressureLevel, memoryInfo)
        }
    }
    
    /**
     * 获取当前内存信息
     */
    private fun getCurrentMemoryInfo(): MemoryInfo {
        val memInfo = ActivityManager.MemoryInfo()
        activityManager.getMemoryInfo(memInfo)
        
        val runtime = Runtime.getRuntime()
        val maxMemory = runtime.maxMemory()
        val totalMemory = runtime.totalMemory()
        val freeMemory = runtime.freeMemory()
        val usedMemory = totalMemory - freeMemory
        val usagePercentage = usedMemory.toFloat() / maxMemory
        
        return MemoryInfo(
            totalMemory = maxMemory,
            availableMemory = memInfo.availMem,
            usedMemory = usedMemory,
            usagePercentage = usagePercentage,
            isLowMemory = memInfo.lowMemory
        )
    }
    
    /**
     * 确定内存压力级别
     */
    private fun determinePressureLevel(memoryInfo: MemoryInfo): MemoryPressureLevel {
        return when {
            memoryInfo.usagePercentage >= MEMORY_PRESSURE_THRESHOLD_CRITICAL || memoryInfo.isLowMemory -> 
                MemoryPressureLevel.CRITICAL
            memoryInfo.usagePercentage >= MEMORY_PRESSURE_THRESHOLD_HIGH -> 
                MemoryPressureLevel.HIGH
            memoryInfo.usagePercentage >= 0.7f -> 
                MemoryPressureLevel.MODERATE
            else -> 
                MemoryPressureLevel.NORMAL
        }
    }
    
    /**
     * 判断是否应该触发缓存清理
     */
    private fun shouldTriggerCacheCleanup(pressureLevel: MemoryPressureLevel, memoryInfo: MemoryInfo): Boolean {
        val now = System.currentTimeMillis()
        val timeSinceLastCheck = now - lastMemoryCheck.get()
        
        return when (pressureLevel) {
            MemoryPressureLevel.CRITICAL -> true // 严重压力立即清理
            MemoryPressureLevel.HIGH -> timeSinceLastCheck > 10000 // 高压力10秒间隔
            MemoryPressureLevel.MODERATE -> timeSinceLastCheck > 30000 // 中度压力30秒间隔
            MemoryPressureLevel.NORMAL -> false // 正常情况不清理
        }
    }
    
    /**
     * 处理内存压力
     */
    private suspend fun handleMemoryPressure(pressureLevel: MemoryPressureLevel, memoryInfo: MemoryInfo) = withContext(Dispatchers.IO) {
        lastMemoryCheck.set(System.currentTimeMillis())
        
        val activeCacheManagers = getActiveCacheManagers()
        Log.d(TAG, "处理内存压力: ${pressureLevel.description}, 活跃缓存管理器: ${activeCacheManagers.size}")
        
        when (pressureLevel) {
            MemoryPressureLevel.MODERATE -> {
                // 轻度压力：清理低优先级缓存
                activeCacheManagers.forEach { (name, manager) ->
                    try {
                        manager.clearLowPriorityCache()
                        Log.d(TAG, "清理低优先级缓存: $name")
                    } catch (e: Exception) {
                        Log.e(TAG, "清理低优先级缓存失败: $name", e)
                    }
                }
            }
            
            MemoryPressureLevel.HIGH -> {
                // 高压力：清理低和中优先级缓存
                activeCacheManagers.forEach { (name, manager) ->
                    try {
                        manager.clearLowPriorityCache()
                        manager.clearMediumPriorityCache()
                        Log.d(TAG, "清理低中优先级缓存: $name")
                    } catch (e: Exception) {
                        Log.e(TAG, "清理低中优先级缓存失败: $name", e)
                    }
                }
                
                // 强制垃圾回收
                System.gc()
            }
            
            MemoryPressureLevel.CRITICAL -> {
                // 严重压力：清理所有缓存
                activeCacheManagers.forEach { (name, manager) ->
                    try {
                        manager.clearAllCache()
                        Log.w(TAG, "清理所有缓存: $name")
                    } catch (e: Exception) {
                        Log.e(TAG, "清理所有缓存失败: $name", e)
                    }
                }
                
                // 强制垃圾回收
                System.gc()
                
                // 记录严重内存压力事件
                logCriticalMemoryEvent(memoryInfo)
            }
            
            MemoryPressureLevel.NORMAL -> {
                // 正常情况，不需要处理
            }
        }
        
        // 清理后再次检查内存状态
        delay(1000)
        val afterCleanupInfo = getCurrentMemoryInfo()
        val memoryFreed = memoryInfo.usedMemory - afterCleanupInfo.usedMemory
        
        if (memoryFreed > 0) {
            Log.d(TAG, "✅ 内存清理完成，释放内存: ${memoryFreed / 1024 / 1024}MB")
        }
    }
    
    /**
     * 获取活跃的缓存管理器
     */
    private fun getActiveCacheManagers(): Map<String, CacheManager> {
        val activeManagers = mutableMapOf<String, CacheManager>()
        
        cacheManagers.entries.removeAll { (name, weakRef) ->
            val manager = weakRef.get()
            if (manager != null) {
                activeManagers[name] = manager
                false // 保留
            } else {
                Log.d(TAG, "移除已回收的缓存管理器: $name")
                true // 移除
            }
        }
        
        return activeManagers
    }
    
    /**
     * 记录详细内存信息
     */
    private fun logDetailedMemoryInfo() {
        val memoryInfo = getCurrentMemoryInfo()
        val pressureLevel = determinePressureLevel(memoryInfo)
        
        Log.d(TAG, buildString {
            appendLine("=== 内存状态报告 ===")
            appendLine("总内存: ${memoryInfo.totalMemory / 1024 / 1024}MB")
            appendLine("已用内存: ${memoryInfo.usedMemory / 1024 / 1024}MB")
            appendLine("可用内存: ${memoryInfo.availableMemory / 1024 / 1024}MB")
            appendLine("使用率: ${(memoryInfo.usagePercentage * 100).toInt()}%")
            appendLine("压力级别: ${pressureLevel.description}")
            appendLine("低内存状态: ${memoryInfo.isLowMemory}")
            
            // 缓存管理器状态
            val activeManagers = getActiveCacheManagers()
            appendLine("活跃缓存管理器: ${activeManagers.size}")
            activeManagers.forEach { (name, manager) ->
                try {
                    val cacheSize = manager.getCacheSize()
                    appendLine("  - $name: ${cacheSize / 1024 / 1024}MB")
                } catch (e: Exception) {
                    appendLine("  - $name: 获取大小失败")
                }
            }
            appendLine("==================")
        })
    }
    
    /**
     * 记录严重内存压力事件
     */
    private fun logCriticalMemoryEvent(memoryInfo: MemoryInfo) {
        Log.w(TAG, buildString {
            appendLine("🚨 严重内存压力事件 🚨")
            appendLine("时间: ${System.currentTimeMillis()}")
            appendLine("使用率: ${(memoryInfo.usagePercentage * 100).toInt()}%")
            appendLine("已用内存: ${memoryInfo.usedMemory / 1024 / 1024}MB")
            appendLine("可用内存: ${memoryInfo.availableMemory / 1024 / 1024}MB")
            appendLine("低内存状态: ${memoryInfo.isLowMemory}")
            appendLine("========================")
        })
    }
    
    /**
     * 获取内存压力统计
     */
    fun getMemoryPressureStats(): String {
        val memoryInfo = lastMemoryInfo ?: getCurrentMemoryInfo()
        val pressureLevel = determinePressureLevel(memoryInfo)
        val activeManagers = getActiveCacheManagers()
        
        return buildString {
            appendLine("=== 内存压力管理统计 ===")
            appendLine("监控状态: ${if (isMonitoring.get()) "运行中" else "已停止"}")
            appendLine("当前压力级别: ${pressureLevel.description}")
            appendLine("内存使用率: ${(memoryInfo.usagePercentage * 100).toInt()}%")
            appendLine("注册的缓存管理器: ${cacheManagers.size}")
            appendLine("活跃的缓存管理器: ${activeManagers.size}")
            appendLine("上次检查时间: ${if (lastMemoryCheck.get() > 0) formatTimestamp(lastMemoryCheck.get()) else "未检查"}")
            appendLine("=====================")
        }
    }
    
    private fun formatTimestamp(timestamp: Long): String {
        val secondsAgo = (System.currentTimeMillis() - timestamp) / 1000
        return when {
            secondsAgo < 60 -> "${secondsAgo}秒前"
            secondsAgo < 3600 -> "${secondsAgo / 60}分钟前"
            else -> "${secondsAgo / 3600}小时前"
        }
    }
    
    // ========== ComponentCallbacks2 实现 ==========
    
    override fun onConfigurationChanged(newConfig: Configuration) {
        // 配置变化时不需要特殊处理
    }
    
    override fun onLowMemory() {
        Log.w(TAG, "🚨 系统低内存回调")
        monitorScope.launch {
            handleMemoryPressure(MemoryPressureLevel.CRITICAL, getCurrentMemoryInfo())
        }
    }
    
    override fun onTrimMemory(level: Int) {
        memoryPressureLevel.set(level.toLong())
        
        val pressureLevel = when (level) {
            ComponentCallbacks2.TRIM_MEMORY_RUNNING_CRITICAL,
            ComponentCallbacks2.TRIM_MEMORY_COMPLETE -> MemoryPressureLevel.CRITICAL
            
            ComponentCallbacks2.TRIM_MEMORY_RUNNING_LOW,
            ComponentCallbacks2.TRIM_MEMORY_MODERATE -> MemoryPressureLevel.HIGH
            
            ComponentCallbacks2.TRIM_MEMORY_RUNNING_MODERATE,
            ComponentCallbacks2.TRIM_MEMORY_BACKGROUND -> MemoryPressureLevel.MODERATE
            
            else -> MemoryPressureLevel.NORMAL
        }
        
        Log.w(TAG, "🔔 系统内存修剪回调: level=$level, 压力级别=${pressureLevel.description}")
        
        monitorScope.launch {
            handleMemoryPressure(pressureLevel, getCurrentMemoryInfo())
        }
    }
    
    /**
     * 销毁管理器
     */
    fun destroy() {
        stopMemoryMonitoring()
        cacheManagers.clear()
        Log.d(TAG, "🔚 MemoryPressureManager已销毁")
    }
}
