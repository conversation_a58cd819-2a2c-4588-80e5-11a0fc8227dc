package com.example.coloringproject.utils

import android.app.ActivityManager
import android.content.Context
import android.os.Debug
import android.os.Process
import android.util.Log
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import java.io.File
import java.text.SimpleDateFormat
import java.util.*
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.atomic.AtomicLong
import kotlin.system.measureTimeMillis

/**
 * 性能监控系统
 * 第三阶段性能优化：全面的性能监控和诊断工具
 */
class PerformanceMonitor private constructor(private val context: Context) {
    
    companion object {
        private const val TAG = "PerformanceMonitor"
        
        // 监控配置
        private const val MONITOR_INTERVAL_MS = 5000L // 5秒监控间隔
        private const val DETAILED_MONITOR_INTERVAL_MS = 30000L // 30秒详细监控间隔
        private const val MAX_PERFORMANCE_RECORDS = 100 // 最大性能记录数
        
        // 性能阈值
        private const val MEMORY_WARNING_THRESHOLD = 0.8f // 80%内存使用率警告
        private const val CPU_WARNING_THRESHOLD = 80.0 // 80% CPU使用率警告
        private const val FRAME_DROP_WARNING_THRESHOLD = 5 // 5帧丢失警告
        
        @Volatile
        private var INSTANCE: PerformanceMonitor? = null
        
        fun getInstance(context: Context): PerformanceMonitor {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: PerformanceMonitor(context.applicationContext).also { INSTANCE = it }
            }
        }
    }
    
    private val activityManager = context.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
    private val monitorScope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    
    // 监控状态
    private var isMonitoring = false
    private val startTime = System.currentTimeMillis()
    
    // 性能数据
    private val performanceRecords = mutableListOf<PerformanceRecord>()
    private val operationMetrics = ConcurrentHashMap<String, OperationMetric>()
    private val frameMetrics = ConcurrentHashMap<String, FrameMetric>()
    
    // 实时状态
    private val _currentPerformance = MutableStateFlow(PerformanceSnapshot.empty())
    val currentPerformance: StateFlow<PerformanceSnapshot> = _currentPerformance
    
    // 统计计数器
    private val totalOperations = AtomicLong(0)
    private val slowOperations = AtomicLong(0)
    private val memoryWarnings = AtomicLong(0)
    private val frameDrops = AtomicLong(0)
    
    /**
     * 性能记录
     */
    data class PerformanceRecord(
        val timestamp: Long,
        val memoryUsage: MemoryUsage,
        val cpuUsage: Double,
        val frameRate: Float,
        val networkStats: NetworkStats,
        val diskStats: DiskStats
    )
    
    /**
     * 内存使用情况
     */
    data class MemoryUsage(
        val totalMemory: Long,
        val usedMemory: Long,
        val availableMemory: Long,
        val usagePercentage: Float,
        val nativeHeapSize: Long,
        val nativeHeapAllocated: Long
    )
    
    /**
     * 网络统计
     */
    data class NetworkStats(
        val bytesReceived: Long,
        val bytesSent: Long,
        val packetsReceived: Long,
        val packetsSent: Long
    )
    
    /**
     * 磁盘统计
     */
    data class DiskStats(
        val totalSpace: Long,
        val freeSpace: Long,
        val usedSpace: Long,
        val cacheSize: Long
    )
    
    /**
     * 操作指标
     */
    data class OperationMetric(
        val operationName: String,
        val totalCalls: Long,
        val totalTime: Long,
        val averageTime: Double,
        val minTime: Long,
        val maxTime: Long,
        val errorCount: Long
    )
    
    /**
     * 帧指标
     */
    data class FrameMetric(
        val screenName: String,
        val totalFrames: Long,
        val droppedFrames: Long,
        val averageFrameTime: Double,
        val maxFrameTime: Long
    )
    
    /**
     * 性能快照
     */
    data class PerformanceSnapshot(
        val timestamp: Long,
        val memoryUsage: MemoryUsage,
        val cpuUsage: Double,
        val frameRate: Float,
        val activeOperations: Int,
        val cacheHitRate: Float,
        val networkLatency: Long,
        val isHealthy: Boolean
    ) {
        companion object {
            fun empty() = PerformanceSnapshot(
                timestamp = 0L,
                memoryUsage = MemoryUsage(0, 0, 0, 0f, 0, 0),
                cpuUsage = 0.0,
                frameRate = 0f,
                activeOperations = 0,
                cacheHitRate = 0f,
                networkLatency = 0L,
                isHealthy = true
            )
        }
    }
    
    /**
     * 开始性能监控
     */
    fun startMonitoring() {
        if (isMonitoring) {
            Log.w(TAG, "性能监控已在运行")
            return
        }
        
        isMonitoring = true
        Log.d(TAG, "🔍 开始性能监控")
        
        // 启动基础监控
        monitorScope.launch {
            while (isMonitoring) {
                try {
                    collectBasicMetrics()
                    delay(MONITOR_INTERVAL_MS)
                } catch (e: Exception) {
                    Log.e(TAG, "基础监控异常", e)
                    delay(MONITOR_INTERVAL_MS * 2)
                }
            }
        }
        
        // 启动详细监控
        monitorScope.launch {
            while (isMonitoring) {
                try {
                    collectDetailedMetrics()
                    delay(DETAILED_MONITOR_INTERVAL_MS)
                } catch (e: Exception) {
                    Log.e(TAG, "详细监控异常", e)
                    delay(DETAILED_MONITOR_INTERVAL_MS * 2)
                }
            }
        }
        
        // 启动性能分析
        monitorScope.launch {
            while (isMonitoring) {
                try {
                    analyzePerformance()
                    delay(DETAILED_MONITOR_INTERVAL_MS)
                } catch (e: Exception) {
                    Log.e(TAG, "性能分析异常", e)
                }
            }
        }
    }
    
    /**
     * 停止性能监控
     */
    fun stopMonitoring() {
        if (!isMonitoring) return
        
        isMonitoring = false
        Log.d(TAG, "🛑 停止性能监控")
        
        // 生成最终报告
        generateFinalReport()
    }
    
    /**
     * 收集基础指标
     */
    private suspend fun collectBasicMetrics() = withContext(Dispatchers.IO) {
        val memoryUsage = getCurrentMemoryUsage()
        val cpuUsage = getCurrentCpuUsage()
        val frameRate = getCurrentFrameRate()
        val networkStats = getCurrentNetworkStats()
        val diskStats = getCurrentDiskStats()
        
        // 创建性能记录
        val record = PerformanceRecord(
            timestamp = System.currentTimeMillis(),
            memoryUsage = memoryUsage,
            cpuUsage = cpuUsage,
            frameRate = frameRate,
            networkStats = networkStats,
            diskStats = diskStats
        )
        
        // 添加到记录列表
        synchronized(performanceRecords) {
            performanceRecords.add(record)
            if (performanceRecords.size > MAX_PERFORMANCE_RECORDS) {
                performanceRecords.removeAt(0)
            }
        }
        
        // 更新实时状态
        val snapshot = PerformanceSnapshot(
            timestamp = record.timestamp,
            memoryUsage = memoryUsage,
            cpuUsage = cpuUsage,
            frameRate = frameRate,
            activeOperations = operationMetrics.size,
            cacheHitRate = calculateCacheHitRate(),
            networkLatency = 0L, // TODO: 实现网络延迟测量
            isHealthy = isSystemHealthy(memoryUsage, cpuUsage, frameRate)
        )
        
        _currentPerformance.value = snapshot
    }
    
    /**
     * 收集详细指标
     */
    private suspend fun collectDetailedMetrics() = withContext(Dispatchers.IO) {
        Log.d(TAG, "收集详细性能指标")
        
        // 收集GC信息
        val gcStats = collectGCStats()
        
        // 收集线程信息
        val threadStats = collectThreadStats()
        
        // 收集文件描述符信息
        val fdStats = collectFileDescriptorStats()
        
        Log.d(TAG, buildString {
            appendLine("=== 详细性能指标 ===")
            appendLine("GC统计: $gcStats")
            appendLine("线程统计: $threadStats")
            appendLine("文件描述符: $fdStats")
            appendLine("==================")
        })
    }
    
    /**
     * 分析性能
     */
    private suspend fun analyzePerformance() = withContext(Dispatchers.IO) {
        val recentRecords = synchronized(performanceRecords) {
            performanceRecords.takeLast(10) // 分析最近10条记录
        }
        
        if (recentRecords.isEmpty()) return@withContext
        
        // 分析内存趋势
        val memoryTrend = analyzeMemoryTrend(recentRecords)
        
        // 分析CPU趋势
        val cpuTrend = analyzeCpuTrend(recentRecords)
        
        // 分析帧率趋势
        val frameTrend = analyzeFrameTrend(recentRecords)
        
        // 检查性能警告
        checkPerformanceWarnings(recentRecords.last())
        
        Log.d(TAG, buildString {
            appendLine("=== 性能趋势分析 ===")
            appendLine("内存趋势: $memoryTrend")
            appendLine("CPU趋势: $cpuTrend")
            appendLine("帧率趋势: $frameTrend")
            appendLine("==================")
        })
    }
    
    /**
     * 记录操作性能
     */
    fun recordOperation(operationName: String, block: () -> Unit) {
        totalOperations.incrementAndGet()
        
        val executionTime = measureTimeMillis {
            try {
                block()
            } catch (e: Exception) {
                // 记录错误
                updateOperationMetric(operationName, 0L, true)
                throw e
            }
        }
        
        updateOperationMetric(operationName, executionTime, false)
        
        // 检查是否为慢操作
        if (executionTime > 1000) { // 超过1秒
            slowOperations.incrementAndGet()
            Log.w(TAG, "⚠️ 慢操作检测: $operationName 耗时 ${executionTime}ms")
        }
    }
    
    /**
     * 记录操作性能（挂起函数版本）
     */
    suspend fun recordSuspendOperation(operationName: String, block: suspend () -> Unit) {
        totalOperations.incrementAndGet()
        
        val executionTime = measureTimeMillis {
            try {
                block()
            } catch (e: Exception) {
                updateOperationMetric(operationName, 0L, true)
                throw e
            }
        }
        
        updateOperationMetric(operationName, executionTime, false)
        
        if (executionTime > 1000) {
            slowOperations.incrementAndGet()
            Log.w(TAG, "⚠️ 慢操作检测: $operationName 耗时 ${executionTime}ms")
        }
    }
    
    /**
     * 记录帧性能
     */
    fun recordFrameMetric(screenName: String, frameTime: Long, dropped: Boolean) {
        val metric = frameMetrics.getOrPut(screenName) {
            FrameMetric(screenName, 0, 0, 0.0, 0)
        }
        
        val updatedMetric = metric.copy(
            totalFrames = metric.totalFrames + 1,
            droppedFrames = if (dropped) metric.droppedFrames + 1 else metric.droppedFrames,
            averageFrameTime = (metric.averageFrameTime * metric.totalFrames + frameTime) / (metric.totalFrames + 1),
            maxFrameTime = maxOf(metric.maxFrameTime, frameTime)
        )
        
        frameMetrics[screenName] = updatedMetric
        
        if (dropped) {
            frameDrops.incrementAndGet()
        }
    }
    
    // ========== 私有辅助方法 ==========
    
    private fun getCurrentMemoryUsage(): MemoryUsage {
        val runtime = Runtime.getRuntime()
        val memInfo = ActivityManager.MemoryInfo()
        activityManager.getMemoryInfo(memInfo)
        
        val totalMemory = runtime.maxMemory()
        val usedMemory = runtime.totalMemory() - runtime.freeMemory()
        val availableMemory = memInfo.availMem
        val usagePercentage = usedMemory.toFloat() / totalMemory
        
        return MemoryUsage(
            totalMemory = totalMemory,
            usedMemory = usedMemory,
            availableMemory = availableMemory,
            usagePercentage = usagePercentage,
            nativeHeapSize = Debug.getNativeHeapSize(),
            nativeHeapAllocated = Debug.getNativeHeapAllocatedSize()
        )
    }
    
    private fun getCurrentCpuUsage(): Double {
        // 简化的CPU使用率计算
        return try {
            val pid = Process.myPid()
            val statFile = File("/proc/$pid/stat")
            if (statFile.exists()) {
                // 这里应该实现更精确的CPU使用率计算
                // 简化返回0.0
                0.0
            } else {
                0.0
            }
        } catch (e: Exception) {
            0.0
        }
    }
    
    private fun getCurrentFrameRate(): Float {
        // 简化的帧率计算，实际应该从Choreographer获取
        return 60.0f
    }
    
    private fun getCurrentNetworkStats(): NetworkStats {
        // 简化的网络统计
        return NetworkStats(0, 0, 0, 0)
    }
    
    private fun getCurrentDiskStats(): DiskStats {
        val cacheDir = context.cacheDir
        val totalSpace = cacheDir.totalSpace
        val freeSpace = cacheDir.freeSpace
        val usedSpace = totalSpace - freeSpace
        val cacheSize = calculateDirectorySize(cacheDir)
        
        return DiskStats(totalSpace, freeSpace, usedSpace, cacheSize)
    }
    
    private fun calculateDirectorySize(dir: File): Long {
        return try {
            dir.walkTopDown().filter { it.isFile }.map { it.length() }.sum()
        } catch (e: Exception) {
            0L
        }
    }
    
    private fun updateOperationMetric(operationName: String, executionTime: Long, isError: Boolean) {
        val metric = operationMetrics.getOrPut(operationName) {
            OperationMetric(operationName, 0, 0, 0.0, Long.MAX_VALUE, 0, 0)
        }
        
        val updatedMetric = if (isError) {
            metric.copy(errorCount = metric.errorCount + 1)
        } else {
            metric.copy(
                totalCalls = metric.totalCalls + 1,
                totalTime = metric.totalTime + executionTime,
                averageTime = (metric.totalTime + executionTime).toDouble() / (metric.totalCalls + 1),
                minTime = minOf(metric.minTime, executionTime),
                maxTime = maxOf(metric.maxTime, executionTime)
            )
        }
        
        operationMetrics[operationName] = updatedMetric
    }
    
    private fun calculateCacheHitRate(): Float {
        // 这里应该从各个缓存管理器获取命中率
        return 0.8f // 简化返回
    }
    
    private fun isSystemHealthy(memoryUsage: MemoryUsage, cpuUsage: Double, frameRate: Float): Boolean {
        return memoryUsage.usagePercentage < MEMORY_WARNING_THRESHOLD &&
                cpuUsage < CPU_WARNING_THRESHOLD &&
                frameRate > 30.0f
    }
    
    private fun analyzeMemoryTrend(records: List<PerformanceRecord>): String {
        if (records.size < 2) return "数据不足"
        
        val first = records.first().memoryUsage.usagePercentage
        val last = records.last().memoryUsage.usagePercentage
        val trend = last - first
        
        return when {
            trend > 0.1f -> "上升趋势 (+${(trend * 100).toInt()}%)"
            trend < -0.1f -> "下降趋势 (${(trend * 100).toInt()}%)"
            else -> "稳定"
        }
    }
    
    private fun analyzeCpuTrend(records: List<PerformanceRecord>): String {
        if (records.size < 2) return "数据不足"
        
        val average = records.map { it.cpuUsage }.average()
        return "平均CPU使用率: ${average.toInt()}%"
    }
    
    private fun analyzeFrameTrend(records: List<PerformanceRecord>): String {
        if (records.size < 2) return "数据不足"
        
        val average = records.map { it.frameRate }.average()
        return "平均帧率: ${average.toInt()}fps"
    }
    
    private fun checkPerformanceWarnings(record: PerformanceRecord) {
        // 检查内存警告
        if (record.memoryUsage.usagePercentage > MEMORY_WARNING_THRESHOLD) {
            memoryWarnings.incrementAndGet()
            Log.w(TAG, "⚠️ 内存使用率过高: ${(record.memoryUsage.usagePercentage * 100).toInt()}%")
        }
        
        // 检查CPU警告
        if (record.cpuUsage > CPU_WARNING_THRESHOLD) {
            Log.w(TAG, "⚠️ CPU使用率过高: ${record.cpuUsage.toInt()}%")
        }
        
        // 检查帧率警告
        if (record.frameRate < 30.0f) {
            Log.w(TAG, "⚠️ 帧率过低: ${record.frameRate.toInt()}fps")
        }
    }
    
    private fun collectGCStats(): String {
        return "GC次数: ${Debug.getGlobalGcInvocationCount()}"
    }
    
    private fun collectThreadStats(): String {
        return "活跃线程数: ${Thread.activeCount()}"
    }
    
    private fun collectFileDescriptorStats(): String {
        return try {
            val fdDir = File("/proc/${Process.myPid()}/fd")
            val fdCount = fdDir.listFiles()?.size ?: 0
            "文件描述符: $fdCount"
        } catch (e: Exception) {
            "无法获取"
        }
    }
    
    private fun generateFinalReport() {
        val uptime = System.currentTimeMillis() - startTime
        val dateFormat = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault())
        
        Log.i(TAG, buildString {
            appendLine("=== 性能监控最终报告 ===")
            appendLine("监控时间: ${uptime / 1000}秒")
            appendLine("结束时间: ${dateFormat.format(Date())}")
            appendLine("总操作数: ${totalOperations.get()}")
            appendLine("慢操作数: ${slowOperations.get()}")
            appendLine("内存警告: ${memoryWarnings.get()}")
            appendLine("帧丢失: ${frameDrops.get()}")
            appendLine("性能记录数: ${performanceRecords.size}")
            appendLine("操作指标数: ${operationMetrics.size}")
            appendLine("帧指标数: ${frameMetrics.size}")
            appendLine("========================")
        })
    }
    
    /**
     * 获取性能统计报告
     */
    fun getPerformanceReport(): String {
        val currentSnapshot = _currentPerformance.value
        
        return buildString {
            appendLine("=== 性能监控报告 ===")
            appendLine("监控状态: ${if (isMonitoring) "运行中" else "已停止"}")
            appendLine("系统健康: ${if (currentSnapshot.isHealthy) "良好" else "异常"}")
            appendLine("内存使用率: ${(currentSnapshot.memoryUsage.usagePercentage * 100).toInt()}%")
            appendLine("CPU使用率: ${currentSnapshot.cpuUsage.toInt()}%")
            appendLine("当前帧率: ${currentSnapshot.frameRate.toInt()}fps")
            appendLine("活跃操作: ${currentSnapshot.activeOperations}")
            appendLine("缓存命中率: ${(currentSnapshot.cacheHitRate * 100).toInt()}%")
            appendLine("总操作数: ${totalOperations.get()}")
            appendLine("慢操作数: ${slowOperations.get()}")
            appendLine("内存警告: ${memoryWarnings.get()}")
            appendLine("帧丢失: ${frameDrops.get()}")
            appendLine("==================")
        }
    }
    
    /**
     * 销毁监控器
     */
    fun destroy() {
        stopMonitoring()
        monitorScope.cancel()
        performanceRecords.clear()
        operationMetrics.clear()
        frameMetrics.clear()
        Log.d(TAG, "🔚 PerformanceMonitor已销毁")
    }
}
