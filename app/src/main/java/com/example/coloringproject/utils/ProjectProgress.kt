package com.example.coloringproject.utils

/**
 * 项目进度数据类
 * 用于替代被删除的ProjectProgress类
 */
data class ProjectProgress(
    val projectName: String,
    val progressPercentage: Int,
    val isCompleted: Boolean,
    val lastModified: Long,
    val filledRegions: Set<Int>,
    val totalRegions: Int,
    val previewImagePath: String? = null
) {
    /**
     * 获取格式化的日期
     */
    fun getFormattedDate(): String {
        val date = java.util.Date(lastModified)
        val format = java.text.SimpleDateFormat("yyyy-MM-dd HH:mm", java.util.Locale.getDefault())
        return format.format(date)
    }
}
