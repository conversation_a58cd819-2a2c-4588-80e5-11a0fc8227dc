package com.example.coloringproject.utils

import android.content.Context
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.util.Log
import com.example.coloringproject.data.ColoringData
import com.google.gson.Gson
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.IOException

/**
 * Assets资源加载器
 * 专门处理从Assets目录加载资源的逻辑
 */
class AssetResourceLoader(private val context: Context) {
    
    companion object {
        private const val TAG = "AssetResourceLoader"
    }
    
    private val gson = Gson()
    
    /**
     * 从Assets加载涂色数据
     */
    suspend fun loadColoringData(projectId: String): ColoringData? = withContext(Dispatchers.IO) {
        try {
            // 尝试多种可能的文件路径
            val possiblePaths = listOf(
                "$projectId.json",
                "${projectId}_outline.json",
                "default/$projectId.json",
                "default/${projectId}_outline.json"
            )
            
            for (path in possiblePaths) {
                try {
                    val inputStream = context.assets.open(path)
                    val jsonString = inputStream.bufferedReader().use { it.readText() }
                    inputStream.close()
                    
                    val coloringData = gson.fromJson(jsonString, ColoringData::class.java)
                    if (coloringData != null) {
                        Log.d(TAG, "✅ 成功从Assets加载JSON: $path")
                        return@withContext coloringData
                    }
                } catch (e: IOException) {
                    // 文件不存在，继续尝试下一个路径
                    continue
                } catch (e: Exception) {
                    Log.w(TAG, "解析JSON失败: $path", e)
                    continue
                }
            }
            
            Log.w(TAG, "❌ 未找到项目JSON文件: $projectId")
            null
            
        } catch (e: Exception) {
            Log.e(TAG, "加载涂色数据失败: $projectId", e)
            null
        }
    }
    
    /**
     * 从Assets加载轮廓图片
     */
    suspend fun loadOutlineBitmap(projectId: String): Bitmap? = withContext(Dispatchers.IO) {
        try {
            // 尝试多种可能的图片路径
            val possiblePaths = listOf(
                "$projectId.png",
                "${projectId}_outline.png",
                "default/$projectId.png",
                "default/${projectId}_outline.png"
            )
            
            for (path in possiblePaths) {
                try {
                    val inputStream = context.assets.open(path)
                    val bitmap = BitmapFactory.decodeStream(inputStream)
                    inputStream.close()
                    
                    if (bitmap != null) {
                        Log.d(TAG, "✅ 成功从Assets加载图片: $path")
                        return@withContext bitmap
                    }
                } catch (e: IOException) {
                    // 文件不存在，继续尝试下一个路径
                    continue
                } catch (e: Exception) {
                    Log.w(TAG, "加载图片失败: $path", e)
                    continue
                }
            }
            
            Log.w(TAG, "❌ 未找到项目图片文件: $projectId")
            null
            
        } catch (e: Exception) {
            Log.e(TAG, "加载轮廓图片失败: $projectId", e)
            null
        }
    }
    
    /**
     * 检查Assets中是否存在指定项目
     */
    suspend fun hasProject(projectId: String): Boolean = withContext(Dispatchers.IO) {
        val possibleJsonPaths = listOf(
            "$projectId.json",
            "${projectId}_outline.json",
            "default/$projectId.json",
            "default/${projectId}_outline.json"
        )
        
        for (path in possibleJsonPaths) {
            try {
                context.assets.open(path).close()
                return@withContext true
            } catch (e: IOException) {
                continue
            }
        }
        
        false
    }
    
    /**
     * 获取Assets中所有可用的项目ID
     */
    suspend fun getAllAvailableProjectIds(): List<String> = withContext(Dispatchers.IO) {
        try {
            val projectIds = mutableSetOf<String>()
            
            // 扫描根目录
            val rootFiles = context.assets.list("") ?: emptyArray()
            for (file in rootFiles) {
                if (file.endsWith(".json")) {
                    val projectId = extractProjectId(file)
                    if (projectId.isNotEmpty()) {
                        projectIds.add(projectId)
                    }
                }
            }
            
            // 扫描default目录
            try {
                val defaultFiles = context.assets.list("default") ?: emptyArray()
                for (file in defaultFiles) {
                    if (file.endsWith(".json")) {
                        val projectId = extractProjectId(file)
                        if (projectId.isNotEmpty()) {
                            projectIds.add(projectId)
                        }
                    }
                }
            } catch (e: IOException) {
                // default目录不存在，忽略
            }
            
            Log.d(TAG, "📊 Assets中找到 ${projectIds.size} 个项目")
            projectIds.toList()
            
        } catch (e: Exception) {
            Log.e(TAG, "扫描Assets项目失败", e)
            emptyList()
        }
    }
    
    /**
     * 从文件名提取项目ID
     */
    private fun extractProjectId(fileName: String): String {
        return when {
            fileName.endsWith("_outline.json") -> {
                fileName.removeSuffix("_outline.json")
            }
            fileName.endsWith(".json") -> {
                fileName.removeSuffix(".json")
            }
            else -> ""
        }
    }
    
    /**
     * 验证项目完整性 (JSON + PNG都存在)
     */
    suspend fun validateProject(projectId: String): Boolean = withContext(Dispatchers.IO) {
        val hasJson = hasJsonFile(projectId)
        val hasPng = hasPngFile(projectId)
        
        val isValid = hasJson && hasPng
        if (isValid) {
            Log.d(TAG, "✅ 项目验证通过: $projectId")
        } else {
            Log.w(TAG, "❌ 项目验证失败: $projectId (JSON: $hasJson, PNG: $hasPng)")
        }
        
        isValid
    }
    
    /**
     * 检查是否存在JSON文件
     */
    private suspend fun hasJsonFile(projectId: String): Boolean = withContext(Dispatchers.IO) {
        val possiblePaths = listOf(
            "$projectId.json",
            "${projectId}_outline.json",
            "default/$projectId.json",
            "default/${projectId}_outline.json"
        )
        
        for (path in possiblePaths) {
            try {
                context.assets.open(path).close()
                return@withContext true
            } catch (e: IOException) {
                continue
            }
        }
        
        false
    }
    
    /**
     * 检查是否存在PNG文件
     */
    private suspend fun hasPngFile(projectId: String): Boolean = withContext(Dispatchers.IO) {
        val possiblePaths = listOf(
            "$projectId.png",
            "${projectId}_outline.png",
            "default/$projectId.png",
            "default/${projectId}_outline.png"
        )
        
        for (path in possiblePaths) {
            try {
                context.assets.open(path).close()
                return@withContext true
            } catch (e: IOException) {
                continue
            }
        }
        
        false
    }
}
