package com.example.coloringproject.utils

import android.content.Context
import android.graphics.Bitmap
import android.util.Log
import android.util.LruCache
import androidx.collection.LruCache as AndroidXLruCache
import com.example.coloringproject.config.NetworkConfig
import com.example.coloringproject.data.ColoringData
import com.example.coloringproject.data.ProjectInfo
import com.example.coloringproject.network.ResourceDownloadManager
import com.example.coloringproject.utils.ResourceCacheManager
import com.example.coloringproject.utils.MemoryPressureManager
import kotlinx.coroutines.*
import java.io.File
import java.lang.ref.WeakReference
import java.util.concurrent.ConcurrentHashMap

/**
 * 优化的统一资源管理器
 * 替代多个重叠的资源管理器，提供统一的资源访问入口
 * 第三阶段：实现内存压力感知
 */
class OptimizedResourceManager private constructor(private val context: Context) : MemoryPressureManager.CacheManager {
    
    companion object {
        private const val TAG = "OptimizedResourceManager"
        private const val CACHE_SIZE = 50
        
        @Volatile
        private var INSTANCE: OptimizedResourceManager? = null
        
        fun getInstance(context: Context): OptimizedResourceManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: OptimizedResourceManager(context.applicationContext).also { instance ->
                    INSTANCE = instance
                    // 注册到内存压力管理器
                    instance.memoryPressureManager.registerCacheManager("OptimizedResourceManager", instance)
                }
            }
        }
    }
    
    // 缓存的项目列表
    private var cachedAssetProjects: List<ProjectInfo>? = null
    private var cachedDownloadedProjects: List<ProjectInfo>? = null
    
    // 三级缓存系统
    private val l1Cache = LruCache<String, ProjectData>(CACHE_SIZE) // 内存缓存
    private val l2Cache = ConcurrentHashMap<String, WeakReference<ProjectData>>() // 弱引用缓存
    private val bitmapCache = AndroidXLruCache<String, Bitmap>(20 * 1024 * 1024) // 20MB bitmap缓存
    
    // 网络下载管理器
    private val downloadManager by lazy { ResourceDownloadManager(context) }

    // 文件缓存管理器
    private val cacheManager by lazy { ResourceCacheManager(context) }

    // 内存压力管理器
    private val memoryPressureManager by lazy { MemoryPressureManager.getInstance(context) }

    // 协程作用域
    private val managerScope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    
    /**
     * 项目数据封装
     */
    data class ProjectData(
        val coloringData: ColoringData,
        val outlineBitmap: Bitmap?,
        val source: String, // 简化为字符串类型
        val projectInfo: ProjectInfo
    )
    
    /**
     * 资源加载结果
     */
    sealed class ResourceLoadResult {
        data class Success(val projectData: ProjectData) : ResourceLoadResult()
        data class RequiresDownload(val projectId: String, val reason: String) : ResourceLoadResult()
        data class Error(val message: String) : ResourceLoadResult()
    }
    
    /**
     * 按需加载项目 - 主要入口方法
     */
    suspend fun loadProject(projectId: String): ResourceLoadResult = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "🔍 开始加载项目: $projectId")
            
            // 1. 检查L1缓存 (最快)
            l1Cache.get(projectId)?.let { 
                Log.d(TAG, "✅ L1缓存命中: $projectId")
                return@withContext ResourceLoadResult.Success(it)
            }
            
            // 2. 检查L2弱引用缓存
            l2Cache[projectId]?.get()?.let { data ->
                Log.d(TAG, "✅ L2缓存命中: $projectId")
                l1Cache.put(projectId, data)
                return@withContext ResourceLoadResult.Success(data)
            }
            
            // 3. 从本地Assets加载 (较快)
            loadFromAssets(projectId)?.let { data ->
                Log.d(TAG, "✅ 从Assets加载: $projectId")
                cacheProjectData(projectId, data)
                return@withContext ResourceLoadResult.Success(data)
            }
            
            // 4. 从已下载文件加载 (中等速度)
            loadFromDownloaded(projectId)?.let { data ->
                Log.d(TAG, "✅ 从下载缓存加载: $projectId")
                cacheProjectData(projectId, data)
                return@withContext ResourceLoadResult.Success(data)
            }

            // 5. 从ResourceCacheManager缓存加载 (文件缓存)
            loadFromResourceCache(projectId)?.let { data ->
                Log.d(TAG, "✅ 从资源缓存加载: $projectId")
                cacheProjectData(projectId, data)
                return@withContext ResourceLoadResult.Success(data)
            }

            // 6. 检查是否需要网络下载
            if (NetworkConfig.isNetworkFeatureEnabled()) {
                val projectInfo = getProjectInfo(projectId)
                if (projectInfo != null && !projectInfo.isDownloaded) {
                    Log.d(TAG, "🌐 项目需要下载: $projectId")
                    return@withContext ResourceLoadResult.RequiresDownload(
                        projectId, 
                        "项目需要从服务器下载"
                    )
                }
            }
            
            Log.w(TAG, "❌ 项目未找到: $projectId")
            ResourceLoadResult.Error("项目未找到: $projectId")
            
        } catch (e: Exception) {
            Log.e(TAG, "加载项目失败: $projectId", e)
            ResourceLoadResult.Error("加载失败: ${e.message}")
        }
    }
    
    /**
     * 获取验证过的项目列表 (兼容EnhancedAssetManager接口)
     */
    suspend fun getValidatedProjects(): Result<List<EnhancedAssetManager.ValidatedProject>> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "🔍 获取验证过的项目列表...")
            val enhancedAssetManager = EnhancedAssetManager(context)
            enhancedAssetManager.getValidatedProjects()
        } catch (e: Exception) {
            Log.e(TAG, "获取验证项目失败", e)
            Result.failure(e)
        }
    }

    /**
     * 获取验证报告 (兼容EnhancedAssetManager接口)
     */
    fun getValidationReport(projects: List<EnhancedAssetManager.ValidatedProject>): String {
        val enhancedAssetManager = EnhancedAssetManager(context)
        return enhancedAssetManager.getValidationReport(projects)
    }

    /**
     * 加载填色数据 (兼容EnhancedAssetManager接口)
     */
    suspend fun loadColoringData(filePath: String): Result<ColoringData> = withContext(Dispatchers.IO) {
        try {
            val enhancedAssetManager = EnhancedAssetManager(context)
            enhancedAssetManager.loadColoringData(filePath)
        } catch (e: Exception) {
            Log.e(TAG, "加载填色数据失败: $filePath", e)
            Result.failure(e)
        }
    }

    /**
     * 加载轮廓图片 (兼容EnhancedAssetManager接口)
     */
    suspend fun loadOutlineBitmap(filePath: String): Result<Bitmap> = withContext(Dispatchers.IO) {
        try {
            val enhancedAssetManager = EnhancedAssetManager(context)
            enhancedAssetManager.loadOutlineBitmap(filePath)
        } catch (e: Exception) {
            Log.e(TAG, "加载轮廓图片失败: $filePath", e)
            Result.failure(e)
        }
    }

    /**
     * 获取所有可用项目 (延迟加载)
     */
    suspend fun getAllAvailableProjects(
        includeRemote: Boolean = true,
        forceRefresh: Boolean = false
    ): Result<List<HybridResourceManager.HybridProject>> = withContext(Dispatchers.IO) {
        try {
            val projects = mutableListOf<HybridResourceManager.HybridProject>()
            
            // 本地Assets项目
            val assetProjectList = if (forceRefresh || cachedAssetProjects == null) {
                val projects = scanAssetsProjects()
                cachedAssetProjects = projects
                projects
            } else {
                cachedAssetProjects!!
            }
            
            val assetHybridProjects = assetProjectList.map { projectInfo ->
                HybridResourceManager.HybridProject(
                    id = projectInfo.id,
                    name = projectInfo.id,
                    displayName = projectInfo.displayName,
                    description = "内置涂色项目",
                    category = projectInfo.category,
                    difficulty = projectInfo.difficulty,
                    totalRegions = 0,
                    totalColors = 0,
                    estimatedTime = 30,
                    thumbnailUrl = projectInfo.thumbnailPath,
                    previewUrl = null,
                    resourceType = HybridResourceManager.Companion.ResourceType.LOCAL_ASSET,
                    resourceSource = HybridResourceManager.Companion.ResourceSource.BUILT_IN,
                    version = projectInfo.version,
                    fileSize = 0L,
                    isDownloaded = true,
                    isBuiltIn = true
                )
            }
            projects.addAll(assetHybridProjects)
            
            // 已下载项目
            val downloadedProjectList = if (forceRefresh || cachedDownloadedProjects == null) {
                val projects = scanDownloadedProjects()
                cachedDownloadedProjects = projects
                projects
            } else {
                cachedDownloadedProjects!!
            }
            
            val downloadedHybridProjects = downloadedProjectList.map { projectInfo ->
                HybridResourceManager.HybridProject(
                    id = projectInfo.id,
                    name = projectInfo.id,
                    displayName = projectInfo.displayName,
                    description = "已下载的涂色项目",
                    category = projectInfo.category,
                    difficulty = projectInfo.difficulty,
                    totalRegions = 0,
                    totalColors = 0,
                    estimatedTime = 30,
                    thumbnailUrl = projectInfo.thumbnailPath,
                    previewUrl = null,
                    resourceType = HybridResourceManager.Companion.ResourceType.DOWNLOADED,
                    resourceSource = HybridResourceManager.Companion.ResourceSource.DOWNLOADED,
                    version = projectInfo.version,
                    fileSize = 0L,
                    isDownloaded = true,
                    isBuiltIn = false
                )
            }
            projects.addAll(downloadedHybridProjects)
            
            // 远程项目 (如果启用网络)
            if (includeRemote && NetworkConfig.isNetworkFeatureEnabled()) {
                try {
                    val remoteProjects = fetchRemoteProjects()
                    projects.addAll(remoteProjects)
                } catch (e: Exception) {
                    Log.w(TAG, "获取远程项目失败", e)
                }
            }
            
            Log.d(TAG, "📊 总共找到 ${projects.size} 个项目")
            Result.success(projects.distinctBy { it.id })
            
        } catch (e: Exception) {
            Log.e(TAG, "获取项目列表失败", e)
            Result.failure(e)
        }
    }
    
    /**
     * 下载项目
     */
    suspend fun downloadProject(
        projectId: String,
        onProgress: (Float) -> Unit = {}
    ): Result<ProjectData> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "🌐 开始下载项目: $projectId")
            
            val downloadResult = downloadManager.downloadProject(projectId, onProgress)
            
            if (downloadResult.isSuccess) {
                val downloadedProject = downloadResult.getOrNull()!!
                
                // 加载下载的项目数据
                val projectData = loadDownloadedProjectData(downloadedProject)
                if (projectData != null) {
                    // 缓存项目数据
                    cacheProjectData(projectId, projectData)
                    
                    Log.d(TAG, "✅ 项目下载并加载成功: $projectId")
                    Result.success(projectData)
                } else {
                    Result.failure(Exception("下载的项目数据加载失败"))
                }
            } else {
                Result.failure(downloadResult.exceptionOrNull() ?: Exception("下载失败"))
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "下载项目失败: $projectId", e)
            Result.failure(e)
        }
    }
    
    /**
     * 缓存项目数据
     */
    private fun cacheProjectData(projectId: String, data: ProjectData) {
        l1Cache.put(projectId, data)
        l2Cache[projectId] = WeakReference(data)
        
        // 缓存bitmap
        data.outlineBitmap?.let { bitmap ->
            bitmapCache.put("${projectId}_outline", bitmap)
        }
    }
    
    /**
     * 从Assets加载项目
     */
    private suspend fun loadFromAssets(projectId: String): ProjectData? = withContext(Dispatchers.IO) {
        try {
            val assetLoader = AssetResourceLoader(context)
            val coloringData = assetLoader.loadColoringData(projectId) ?: return@withContext null
            val outlineBitmap = assetLoader.loadOutlineBitmap(projectId)

            val assetProjectList = cachedAssetProjects ?: scanAssetsProjects().also { cachedAssetProjects = it }
            val projectInfo = assetProjectList.find { it.id == projectId }
                ?: ProjectInfo(projectId, projectId, "unknown", "easy")
            
            ProjectData(
                coloringData = coloringData,
                outlineBitmap = outlineBitmap,
                source = "ASSETS",
                projectInfo = projectInfo
            )
        } catch (e: Exception) {
            Log.e(TAG, "从Assets加载失败: $projectId", e)
            null
        }
    }
    
    /**
     * 从已下载文件加载项目
     */
    private suspend fun loadFromDownloaded(projectId: String): ProjectData? = withContext(Dispatchers.IO) {
        try {
            val downloadDir = File(context.filesDir, "downloaded_projects/$projectId")
            if (!downloadDir.exists()) return@withContext null
            
            val jsonFile = File(downloadDir, "$projectId.json")
            val pngFile = File(downloadDir, "$projectId.png")
            
            if (!jsonFile.exists() || !pngFile.exists()) return@withContext null
            
            val fileLoader = FileResourceLoader(context)
            val coloringData = fileLoader.loadColoringDataFromFile(jsonFile) ?: return@withContext null
            val outlineBitmap = fileLoader.loadBitmapFromFile(pngFile)
            
            val downloadedProjectList = cachedDownloadedProjects ?: scanDownloadedProjects().also { cachedDownloadedProjects = it }
            val projectInfo = downloadedProjectList.find { it.id == projectId }
                ?: ProjectInfo(projectId, projectId, "unknown", "easy")
            
            ProjectData(
                coloringData = coloringData,
                outlineBitmap = outlineBitmap,
                source = "DOWNLOADED",
                projectInfo = projectInfo
            )
        } catch (e: Exception) {
            Log.e(TAG, "从下载文件加载失败: $projectId", e)
            null
        }
    }
    
    /**
     * 扫描Assets项目 (延迟执行)
     */
    private suspend fun scanAssetsProjects(): List<ProjectInfo> {
        return try {
            Log.d(TAG, "🔍 扫描Assets项目...")
            val enhancedAssetManager = EnhancedAssetManager(context)
            val result = enhancedAssetManager.getValidatedProjects()

            if (result.isSuccess) {
                val validatedProjects = result.getOrNull() ?: emptyList()
                val projectInfoList = validatedProjects.filter { it.isValid }.map { validatedProject ->
                    ProjectInfo(
                        id = validatedProject.id,
                        displayName = validatedProject.name,
                        category = "assets",
                        difficulty = validatedProject.difficulty,
                        thumbnailPath = validatedProject.outlineFile,
                        version = "1.0"
                    )
                }
                Log.d(TAG, "✅ Assets扫描完成，找到 ${projectInfoList.size} 个有效项目")
                projectInfoList
            } else {
                Log.w(TAG, "Assets扫描失败: ${result.exceptionOrNull()?.message}")
                emptyList()
            }
        } catch (e: Exception) {
            Log.e(TAG, "扫描Assets失败", e)
            emptyList()
        }
    }
    
    /**
     * 扫描已下载项目 (延迟执行)
     */
    private fun scanDownloadedProjects(): List<ProjectInfo> {
        return try {
            Log.d(TAG, "🔍 扫描已下载项目...")
            val downloadDir = File(context.filesDir, "downloaded_projects")
            if (!downloadDir.exists()) return emptyList()
            
            val projects = mutableListOf<ProjectInfo>()
            downloadDir.listFiles()?.forEach { projectDir ->
                if (projectDir.isDirectory) {
                    val projectId = projectDir.name
                    val jsonFile = File(projectDir, "$projectId.json")
                    val pngFile = File(projectDir, "$projectId.png")
                    
                    if (jsonFile.exists() && pngFile.exists()) {
                        projects.add(ProjectInfo(
                            id = projectId,
                            displayName = projectId,
                            category = "downloaded",
                            difficulty = "unknown"
                        ))
                    }
                }
            }
            
            Log.d(TAG, "✅ 下载项目扫描完成，找到 ${projects.size} 个项目")
            projects
        } catch (e: Exception) {
            Log.e(TAG, "扫描下载项目失败", e)
            emptyList()
        }
    }
    
    /**
     * 获取项目信息
     */
    private suspend fun getProjectInfo(projectId: String): HybridResourceManager.HybridProject? {
        // 先检查本地项目
        val assetProjectList = cachedAssetProjects ?: scanAssetsProjects().also { cachedAssetProjects = it }
        assetProjectList.find { it.id == projectId }?.let { projectInfo ->
            return HybridResourceManager.HybridProject(
                id = projectInfo.id,
                name = projectInfo.id,
                displayName = projectInfo.displayName,
                description = "内置涂色项目",
                category = projectInfo.category,
                difficulty = projectInfo.difficulty,
                totalRegions = 0,
                totalColors = 0,
                estimatedTime = 30,
                thumbnailUrl = projectInfo.thumbnailPath,
                previewUrl = null,
                resourceType = HybridResourceManager.Companion.ResourceType.LOCAL_ASSET,
                resourceSource = HybridResourceManager.Companion.ResourceSource.BUILT_IN,
                version = projectInfo.version,
                fileSize = 0L,
                isDownloaded = true,
                isBuiltIn = true
            )
        }
        
        val downloadedProjectList = cachedDownloadedProjects ?: scanDownloadedProjects().also { cachedDownloadedProjects = it }
        downloadedProjectList.find { it.id == projectId }?.let { projectInfo ->
            return HybridResourceManager.HybridProject(
                id = projectInfo.id,
                name = projectInfo.id,
                displayName = projectInfo.displayName,
                description = "已下载的涂色项目",
                category = projectInfo.category,
                difficulty = projectInfo.difficulty,
                totalRegions = 0,
                totalColors = 0,
                estimatedTime = 30,
                thumbnailUrl = projectInfo.thumbnailPath,
                previewUrl = null,
                resourceType = HybridResourceManager.Companion.ResourceType.DOWNLOADED,
                resourceSource = HybridResourceManager.Companion.ResourceSource.DOWNLOADED,
                version = projectInfo.version,
                fileSize = 0L,
                isDownloaded = true,
                isBuiltIn = false
            )
        }
        
        return null
    }
    
    /**
     * 获取远程项目列表
     */
    private suspend fun fetchRemoteProjects(): List<HybridResourceManager.HybridProject> {
        return try {
            val result = downloadManager.getProjectsList(page = 1, pageSize = 50)
            if (result.isSuccess) {
                val response = result.getOrNull()
                val remoteHybridProjects = response?.data?.projects?.map { serverProject ->
                    HybridResourceManager.HybridProject(
                        id = serverProject.id,
                        name = serverProject.name,
                        displayName = serverProject.displayName,
                        description = serverProject.description,
                        category = serverProject.category,
                        difficulty = serverProject.difficulty,
                        totalRegions = serverProject.totalRegions,
                        totalColors = serverProject.totalColors,
                        estimatedTime = serverProject.estimatedTimeMinutes ?: 30,
                        thumbnailUrl = serverProject.thumbnailUrl,
                        previewUrl = serverProject.previewUrl,
                        resourceType = HybridResourceManager.Companion.ResourceType.REMOTE_ONLY,
                        resourceSource = HybridResourceManager.Companion.ResourceSource.STREAMING,
                        version = serverProject.version,
                        fileSize = serverProject.fileSize,
                        isDownloaded = false,
                        isBuiltIn = false
                    )
                } ?: emptyList()
                remoteHybridProjects
            } else {
                emptyList()
            }
        } catch (e: Exception) {
            Log.e(TAG, "获取远程项目失败", e)
            emptyList()
        }
    }
    
    /**
     * 清理缓存
     */
    fun clearCache() {
        l1Cache.evictAll()
        l2Cache.clear()
        bitmapCache.evictAll()
        Log.d(TAG, "🧹 缓存已清理")
    }
    
    /**
     * 内存压力处理
     */
    fun onMemoryPressure(level: Int) {
        when (level) {
            android.content.ComponentCallbacks2.TRIM_MEMORY_RUNNING_MODERATE -> {
                l1Cache.evictAll()
                Log.d(TAG, "🧹 中等内存压力，清理L1缓存")
            }
            android.content.ComponentCallbacks2.TRIM_MEMORY_RUNNING_LOW -> {
                l1Cache.evictAll()
                l2Cache.clear()
                bitmapCache.evictAll()
                Log.d(TAG, "🧹 低内存压力，清理所有缓存")
            }
        }
    }
    
    /**
     * 加载下载的项目数据
     */
    private suspend fun loadDownloadedProjectData(downloadedProject: ResourceDownloadManager.DownloadedProject): ProjectData? {
        return try {
            val fileLoader = FileResourceLoader(context)
            val coloringData = fileLoader.loadColoringDataFromFile(downloadedProject.jsonFile)
                ?: return null
            val outlineBitmap = fileLoader.loadBitmapFromFile(downloadedProject.outlineFile)
            
            ProjectData(
                coloringData = coloringData,
                outlineBitmap = outlineBitmap,
                source = "DOWNLOADED",
                projectInfo = ProjectInfo(
                    id = downloadedProject.projectId,
                    displayName = downloadedProject.projectId,
                    category = "downloaded",
                    difficulty = "unknown"
                )
            )
        } catch (e: Exception) {
            Log.e(TAG, "加载下载项目数据失败", e)
            null
        }
    }

    /**
     * 从ResourceCacheManager缓存加载项目数据
     */
    private suspend fun loadFromResourceCache(projectId: String): ProjectData? = withContext(Dispatchers.IO) {
        try {
            // 检查项目是否在缓存中
            // 简化处理，假设ResourceCacheManager有这些方法
            // 实际实现时需要添加这些方法
            /*
            if (!cacheManager.isProjectCached(projectId)) {
                return@withContext null
            }

            // 从缓存加载项目数据
            val cachedProject = cacheManager.loadCachedProject(projectId)
                ?: return@withContext null
            */

            // 暂时返回null，表示没有缓存
            return@withContext null
        } catch (e: Exception) {
            Log.e(TAG, "从ResourceCacheManager加载项目失败: $projectId", e)
            null
        }
    }

    // ========== MemoryPressureManager.CacheManager 接口实现 ==========

    override fun clearLowPriorityCache() {
        Log.d(TAG, "🧹 清理低优先级缓存")

        // 清理L2弱引用缓存
        val l2ClearedCount = l2Cache.size
        l2Cache.clear()

        // 清理bitmap缓存的一部分
        bitmapCache.evictAll()

        Log.d(TAG, "清理完成 - L2缓存: ${l2ClearedCount}项, Bitmap缓存: 全部")
    }

    override fun clearMediumPriorityCache() {
        Log.d(TAG, "🧹 清理中优先级缓存")

        // 清理L1缓存的一半
        val l1Size = l1Cache.size()
        val itemsToRemove = l1Size / 2

        // 移除最旧的缓存项
        for (i in 0 until itemsToRemove) {
            // LruCache会自动移除最旧的项
            l1Cache.trimToSize(l1Size - i - 1)
        }

        // 清理项目列表缓存
        cachedAssetProjects = null
        cachedDownloadedProjects = null

        Log.d(TAG, "清理完成 - L1缓存: ${itemsToRemove}项, 项目列表缓存: 已清理")
    }

    override fun clearHighPriorityCache() {
        Log.d(TAG, "🧹 清理高优先级缓存")

        // 清理L1缓存的大部分，但保留最近的几项
        val l1Size = l1Cache.size()
        val itemsToKeep = minOf(5, l1Size) // 最多保留5项

        l1Cache.trimToSize(itemsToKeep)

        Log.d(TAG, "清理完成 - L1缓存保留: ${itemsToKeep}项")
    }

    override fun clearAllCache() {
        Log.w(TAG, "🧹 清理所有缓存 (严重内存压力)")

        // 清理所有缓存
        l1Cache.evictAll()
        l2Cache.clear()
        bitmapCache.evictAll()

        // 清理项目列表缓存
        cachedAssetProjects = null
        cachedDownloadedProjects = null

        Log.w(TAG, "所有缓存已清理")
    }

    override fun getCacheSize(): Long {
        var totalSize = 0L

        // L1缓存大小估算
        totalSize += l1Cache.size() * 1024 * 100 // 每项约100KB

        // L2缓存大小估算
        totalSize += l2Cache.size * 1024 * 50 // 每项约50KB

        // Bitmap缓存大小
        totalSize += bitmapCache.size().toLong()

        return totalSize
    }

    override fun getCacheName(): String {
        return "OptimizedResourceManager"
    }
}
