package com.example.coloringproject.callback

import android.graphics.Bitmap
import com.example.coloringproject.data.ColoringData
import com.example.coloringproject.utils.EnhancedAssetManager

/**
 * 项目加载回调接口
 */
interface ProjectLoadCallback {
    /**
     * 项目加载成功
     */
    fun onProjectLoaded(coloringData: ColoringData, outlineBitmap: Bitmap)
    
    /**
     * 项目加载失败
     */
    fun onProjectLoadError(title: String, message: String)
    
    /**
     * 显示项目选择器
     */
    fun onShowProjectSelector(projects: List<EnhancedAssetManager.ValidatedProject>)
    
    /**
     * 开始加载状态
     */
    fun onLoadingStarted()
    
    /**
     * 加载完成状态
     */
    fun onLoadingCompleted()
}

/**
 * 颜色变化回调接口
 */
interface ColorChangeCallback {
    /**
     * 颜色被选中
     */
    fun onColorSelected(colorHex: String)

    /**
     * 某种颜色完成
     */
    fun onColorCompleted(colorHex: String, regionCount: Int)

    /**
     * 项目完成
     */
    fun onProjectCompleted()

    /**
     * 颜色进度更新
     */
    fun onColorProgressUpdated(colorIndex: Int, progress: Int)

    /**
     * 自动切换到下一个颜色
     */
    fun onAutoSwitchToNextColor(newColor: String)
}

/**
 * 进度管理回调接口
 */
interface ProgressCallback {
    /**
     * 进度保存成功
     */
    fun onProgressSaved(projectName: String)
    
    /**
     * 进度保存失败
     */
    fun onProgressSaveError(projectName: String, error: String)
    
    /**
     * 进度恢复成功
     */
    fun onProgressRestored(projectName: String, filledRegions: Set<Int>)
    
    /**
     * 进度恢复失败
     */
    fun onProgressRestoreError(projectName: String, error: String)
    
    /**
     * 预览图生成完成
     */
    fun onPreviewImageGenerated(projectName: String, previewPath: String?)
}

/**
 * UI状态回调接口
 */
interface UIStateCallback {
    /**
     * 显示加载状态
     */
    fun onShowLoading()
    
    /**
     * 显示就绪状态
     */
    fun onShowReady()
    
    /**
     * 显示错误状态
     */
    fun onShowError(message: String)
    
    /**
     * 显示填色UI
     */
    fun onShowColoringUI()
    
    /**
     * 隐藏填色UI
     */
    fun onHideColoringUI()
}
