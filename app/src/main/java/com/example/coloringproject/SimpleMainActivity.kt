package com.example.coloringproject

import android.graphics.Bitmap
import android.graphics.Color
import android.graphics.drawable.GradientDrawable
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.util.Log
import android.view.Menu
import android.view.MenuItem
import android.view.View
import android.widget.TextView
import android.widget.Toast
import androidx.activity.viewModels
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import com.example.coloringproject.adapter.ImmersiveColorAdapter
import com.example.coloringproject.data.ColorPalette
import com.example.coloringproject.data.ColoringData
import com.example.coloringproject.data.Metadata
import com.example.coloringproject.data.Region
import com.example.coloringproject.data.TouchResult
import com.example.coloringproject.databinding.ActivitySimpleMainBinding
import com.example.coloringproject.manager.ProjectPreloadManager
import com.example.coloringproject.model.ColorInfo
import com.example.coloringproject.network.ResourceDownloadManager
import com.example.coloringproject.ui.ProjectSelectionDialog
import com.example.coloringproject.utils.EnhancedAssetManager
import com.example.coloringproject.utils.HybridResourceManager
import com.example.coloringproject.utils.ImageSaver
import com.example.coloringproject.utils.LibraryEventManager
import com.example.coloringproject.utils.PermissionHelper
import com.example.coloringproject.utils.ProjectNameUtils
import com.example.coloringproject.utils.SimpleAssetManager
import com.example.coloringproject.viewmodel.ColoringViewModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.async
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.io.File

/**
 * 新绘制页面 - 现代化的填色界面
 * 参考设计图优化的简洁绘制体验
 */
class SimpleMainActivity : AppCompatActivity() {

    private val TAG: String = SimpleMainActivity::class.java.name

    // ViewBinding - 所有视图现在通过binding访问
    private lateinit var binding: ActivitySimpleMainBinding

    // 沉浸式界面相关
    private var hintCount = 10000 // 提醒次数
    
    private lateinit var immersiveColorAdapter: ImmersiveColorAdapter
    private lateinit var assetManager: SimpleAssetManager
    private lateinit var enhancedAssetManager: EnhancedAssetManager
    private lateinit var hybridResourceManager: HybridResourceManager
    private lateinit var preloadManager: ProjectPreloadManager

    private var currentColoringData: ColoringData? = null
    private var currentSelectedColor: ColorPalette? = null
    private val filledRegions = mutableSetOf<Int>()
    private var hintsEnabled = true // 默认开启马赛克提醒

    // 缓存处理后的调色板，避免重复计算
    private var cachedProcessedPalette: List<ColorPalette> = emptyList()

    // 自动演示相关
    private var isAutoDemo = false
    private val autoHandler = Handler(Looper.getMainLooper())
    private var autoRunnable: Runnable? = null
    private var currentRegionIndex = 0
    private val viewModel: ColoringViewModel by viewModels()

    // 内存管理器
//    private val memoryManager = com.example.coloringproject.utils.MemoryManager()

    /**
     * 获取当前填色数据 - 供管理器访问
     */
    fun getCurrentColoringData(): ColoringData? = currentColoringData

    /**
     * 获取已填色区域集合 - 供管理器访问
     */
    fun getFilledRegions(): MutableSet<Int> = filledRegions

    /**
     * 更新已填色区域集合 - 供管理器访问
     */
    fun updateFilledRegions(regions: Set<Int>) {
        filledRegions.clear()
        filledRegions.addAll(regions)
    }

    /**
     * 获取标准化项目名称 - 供管理器访问
     */
    fun getStandardizedProjectName(): String {
        return ProjectNameUtils.getUnifiedProjectId(intent = intent)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        // 初始化全局异常处理
//        com.example.coloringproject.utils.CrashHandler.instance.init(this)
        
        binding = ActivitySimpleMainBinding.inflate(layoutInflater)
        setContentView(binding.root)
        Log.d("SimpleMainActivity", "onCreate started")

        // 设置共享元素过渡
        setupSharedElementTransition()

        // 显示网络功能状态
        Log.i(TAG, com.example.coloringproject.config.NetworkConfig.getFeatureStatusReport())

        initViews()

        setupUI()

        assetManager = SimpleAssetManager(this)
        enhancedAssetManager = EnhancedAssetManager(this)
        hybridResourceManager = HybridResourceManager(this)
        preloadManager = ProjectPreloadManager.getInstance(this)
        
        // 启动内存监控
//        memoryManager.startMemoryMonitoring(this)

        // 检查是否有指定的项目信息
        val projectId = intent.getStringExtra("project_id")
        val projectName = intent.getStringExtra("project_name")
        val projectSource = intent.getStringExtra("project_source")
        val fromGallery = intent.getBooleanExtra("from_gallery", false)
        val hasProgress = intent.getBooleanExtra("has_progress", false)
        val hasPreloadedData = intent.getBooleanExtra("has_preloaded_data", false)

        // 强制输出日志，确保能看到
        println("=== SimpleMainActivity Intent参数检查 ===")
        println("projectId: $projectId")
        println("projectName: $projectName")
        println("projectSource: $projectSource")
        println("fromGallery: $fromGallery")
        println("hasProgress: $hasProgress")
        println("hasPreloadedData: $hasPreloadedData")
        println("intent extras: ${intent.extras}")
        println("===========================================")

       Log.e(TAG, "=== Intent参数检查 ===")
       Log.e(TAG, "projectId: $projectId")
       Log.e(TAG, "projectName: $projectName")
       Log.e(TAG, "projectSource: $projectSource")
       Log.e(TAG, "fromGallery: $fromGallery")
       Log.e(TAG, "hasProgress: $hasProgress")
       Log.e(TAG, "hasPreloadedData: $hasPreloadedData")
       Log.e(TAG, "intent extras: ${intent.extras}")
       Log.e(TAG, "===================")

        if (projectId != null) {
            // 检查是否有预加载数据
            if (hasPreloadedData) {
                val preloadedData = preloadManager.getPreloadedProject(projectId)
                if (preloadedData != null) {
                    Log.d(TAG, "使用预加载数据快速启动: $projectId")
                    initializeWithPreloadedData(preloadedData)
                    return
                } else {
                    Log.w(TAG, "标记有预加载数据但实际未找到，回退到常规加载: $projectId")
                }
            }

            // 新的项目加载方式：根据项目ID和来源加载
            Log.d(TAG, "使用项目ID加载: $projectId, 来源: $projectSource")

            // 如果是animal-9项目且在Debug模式下，运行详细检查
            if (projectId == "animal-9" && BuildConfig.DEBUG) {
                Log.i(TAG, "检测到animal-9项目，运行详细检查...")
                lifecycleScope.launch {
                    try {
                        val validationResult = com.example.coloringproject.test.SimpleProjectDebugger.validateProject(this@SimpleMainActivity, projectId)
                        Log.i(TAG, "Animal-9验证结果: $validationResult")

                        if (!validationResult.isValid) {
                            runOnUiThread {
                                showProjectLoadError(
                                    "Animal-9项目验证失败",
                                    validationResult.getErrorSummary()
                                )
                                return@runOnUiThread
                            }
                        }
                    } catch (e: Exception) {
                        Log.e(TAG, "Animal-9项目检查异常", e)
                    }
                }
            }

            loadProjectById(projectId, projectSource)
        } else if (projectName != null) {
            // 兼容旧的项目名称方式
            if (fromGallery) {
                Log.d(TAG, "从Gallery加载项目: $projectName, 有进度: $hasProgress")
                loadProjectFromGallery(projectName, hasProgress)
            } else {
                Log.d(TAG, "使用项目名称加载: $projectName")
                loadSpecificProject(projectName)
            }
        } else {
            // 加载第一个验证过的项目
            Log.d(TAG, "加载默认项目")
            loadFirstValidatedProject()
        }
        
        Log.d("SimpleMainActivity", "onCreate completed")
    }
    
    private fun initViews() {
        // 使用ViewBinding访问视图
        with(binding) {
            // 设置返回按钮 - 使用与onBackPressed相同的逻辑
            btnBack.setOnClickListener {
                onBackPressed()
            }

            // 设置商店按钮
            binding.btnShop.setOnClickListener {
                // TODO: 打开商店页面
            }

            // 设置涂色提醒按钮
            binding.btnHint.setOnClickListener {
                if (hintCount > 0) {
                    useHint()
                }
            }

            // 设置缩放控制
            binding.btnZoomIn.setOnClickListener {
                binding.coloringView.zoomIn()
            }

            binding.btnZoomOut.setOnClickListener {
                binding.coloringView.zoomOut()
            }

            binding.btnZoomFit.setOnClickListener {
                binding.coloringView.zoomToFit()
            }

            // 设置测试功能按钮
            var isTestButtonsVisible = false
            binding.btnHint.setOnLongClickListener {
                isTestButtonsVisible = !isTestButtonsVisible
                testButtonsGroup.visibility = if (isTestButtonsVisible) View.VISIBLE else View.GONE
                true
            }

            // 更新提醒次数显示
            updateHintCountDisplay()
        }

    }

    private fun setupUI() {
        // 设置沉浸式调色板
        immersiveColorAdapter = ImmersiveColorAdapter(emptyList()) { colorInfo ->
            Log.d(TAG, "选择颜色: ${colorInfo.name}")
            // 转换为ColorPalette格式
            val colorPalette = ColorPalette(
                id = colorInfo.id,
                colorHex = colorInfo.hexColor,
                colorRgb = listOf(
                    (colorInfo.colorRgb shr 16) and 0xFF,
                    (colorInfo.colorRgb shr 8) and 0xFF,
                    colorInfo.colorRgb and 0xFF
                ),
                name = colorInfo.name,
                usageCount = colorInfo.usageCount
            )
            selectColor(colorPalette)
        }

        binding.recyclerViewColors.apply {
            layoutManager = LinearLayoutManager(this@SimpleMainActivity, LinearLayoutManager.HORIZONTAL, false)
            adapter = immersiveColorAdapter
        }

        // 设置填色View事件
        binding.coloringView.onRegionTouched = { result ->
            handleRegionTouch(result)
        }
        
        // 初始状态
        showLoading()
    }

    /**
     * 加载指定名称的项目
     */
    private fun loadSpecificProject(projectName: String) {
        lifecycleScope.launch {
            try {
                Log.i(TAG, "开始加载指定项目: $projectName")

                val projectsResult = enhancedAssetManager.getValidatedProjects()
                if (projectsResult.isFailure) {
                    Log.e(TAG, "项目验证失败", projectsResult.exceptionOrNull())
                    runOnUiThread {
                        showError("项目验证失败: ${projectsResult.exceptionOrNull()?.message}")
                    }
                    return@launch
                }

                val projects = projectsResult.getOrNull()!!
                val targetProject = projects.find { it.name == projectName && it.isValid }

                if (targetProject == null) {
                    Log.w(TAG, "未找到指定项目: $projectName")
                    runOnUiThread {
                        showError("未找到项目: $projectName")
                        // 回退到加载第一个有效项目
                        loadFirstValidatedProject()
                    }
                    return@launch
                }

                Log.i(TAG, "找到目标项目: ${targetProject.name}")
                loadValidatedProject(targetProject)

            } catch (e: Exception) {
                Log.e(TAG, "加载指定项目失败: $projectName", e)
                runOnUiThread {
                    showError("加载项目失败: ${e.message}")
                }
            }
        }
    }

    /**
     * 从Gallery加载项目（优先从保存的进度加载）
     */
    private fun loadProjectFromGallery(projectName: String, hasProgress: Boolean) {
        lifecycleScope.launch {
            try {
                val startTime = System.currentTimeMillis()
                Log.i(TAG, "🚀 [性能] Gallery加载开始: $projectName, 有进度: $hasProgress")

                if (hasProgress) {
                    // 步骤1：并行加载进度数据和outline
                    val step1Start = System.currentTimeMillis()
                    val projectSaveManager = com.example.coloringproject.utils.ProjectSaveManager(this@SimpleMainActivity)

                    val progressDeferred = async {
                        val progressStart = System.currentTimeMillis()
                        val result = projectSaveManager.loadFullProgress(projectName)
                        val progressTime = System.currentTimeMillis() - progressStart
                        Log.d(TAG, "📊 [性能] 进度数据加载耗时: ${progressTime}ms")
                        result
                    }
                    val outlineDeferred = async {
                        val outlineStart = System.currentTimeMillis()
                        val result = enhancedAssetManager.loadOutlineBitmap("$projectName.png")
                        val outlineTime = System.currentTimeMillis() - outlineStart
                        Log.d(TAG, "📊 [性能] Outline图片加载耗时: ${outlineTime}ms")
                        result
                    }

                    val progressResult = progressDeferred.await()
                    val outlineResult = outlineDeferred.await()
                    val step1Time = System.currentTimeMillis() - step1Start
                    Log.d(TAG, "📊 [性能] 步骤1-并行加载总耗时: ${step1Time}ms")

                    when (progressResult) {
                        is com.example.coloringproject.utils.LoadResult.Success -> {
                            val fullProgressData = progressResult.data

                            outlineResult.fold(
                                onSuccess = { outlineBitmap ->
                                    val dataLoadTime = System.currentTimeMillis() - startTime
                                    Log.d(TAG, "📊 [性能] 数据加载完成，耗时: ${dataLoadTime}ms")

                                    // 步骤2：UI设置
                                    val uiStart = System.currentTimeMillis()
                                    runOnUiThread {
                                        val showReadyStart = System.currentTimeMillis()
                                        showReady()
                                        val showReadyTime = System.currentTimeMillis() - showReadyStart
                                        Log.d(TAG, "📊 [性能] showReady耗时: ${showReadyTime}ms")

                                        // 步骤3：快速项目设置
                                        val setupStart = System.currentTimeMillis()
                                        setupProjectFast(fullProgressData.coloringData, outlineBitmap, fullProgressData.filledRegions)
                                        val setupTime = System.currentTimeMillis() - setupStart
                                        Log.d(TAG, "📊 [性能] setupProjectFast耗时: ${setupTime}ms")

                                        val uiTime = System.currentTimeMillis() - uiStart
                                        val totalTime = System.currentTimeMillis() - startTime
                                        Log.d(TAG, "📊 [性能] UI设置总耗时: ${uiTime}ms")
                                        Log.i(TAG, "✅ [性能] Gallery快速加载完成: $projectName (总耗时: ${totalTime}ms)")

                                        // 性能分析
                                        if (totalTime > 300) {
                                            Log.w(TAG, "⚠️ [性能] 加载超过300ms目标，需要优化")
                                        } else {
                                            Log.i(TAG, "🎯 [性能] 加载在300ms内完成！")
                                        }
                                    }
                                },
                                onFailure = { error ->
                                    Log.w(TAG, "加载outline失败，回退到普通加载: $error")
                                    loadSpecificProject(projectName)
                                }
                            )
                            return@launch
                        }
                        is com.example.coloringproject.utils.LoadResult.Error -> {
                            Log.w(TAG, "无法从保存进度加载，回退到普通加载: ${progressResult.message}")
                        }
                    }
                }

                // 如果没有进度或加载进度失败，使用普通方式加载
                Log.i(TAG, "使用普通方式加载项目: $projectName")
                loadSpecificProject(projectName)

            } catch (e: Exception) {
                Log.e(TAG, "从Gallery加载项目失败: $projectName", e)
                runOnUiThread {
                    showError("加载项目失败: ${e.message}")
                }
            }
        }
    }

    private fun loadFirstValidatedProject() {
        lifecycleScope.launch {
            try {
                Log.i(TAG, "开始验证和加载项目...")

                val projectsResult = enhancedAssetManager.getValidatedProjects()
                if (projectsResult.isFailure) {
                    Log.e(TAG, "项目验证失败", projectsResult.exceptionOrNull())
                    runOnUiThread {
                        showError("项目验证失败: ${projectsResult.exceptionOrNull()?.message}")
                    }
                    return@launch
                }

                val projects = projectsResult.getOrNull()!!
                val validProjects = projects.filter { it.isValid }

                Log.i(TAG, "项目验证完成: 总计 ${projects.size} 个项目, 有效 ${validProjects.size} 个")

                // 显示验证报告
                val report = enhancedAssetManager.getValidationReport(projects)
                Log.i(TAG, "验证报告:\n$report")

                if (validProjects.isEmpty()) {
                    Log.e(TAG, "没有找到有效的项目")
                    runOnUiThread {
                        showError("没有找到有效的填色项目\n\n检查assets文件夹中是否有匹配的JSON和PNG文件")
                    }
                    return@launch
                }

                // 加载第一个有效项目
                val firstProject = validProjects.first()
                Log.i(TAG, "加载项目: ${firstProject.name}")
                loadValidatedProject(firstProject)

                // 如果有多个项目，在UI中显示选择选项
                if (validProjects.size > 1) {
                    runOnUiThread {
                        showProjectSelector(validProjects)
                    }
                }

            } catch (e: Exception) {
                Log.e(TAG, "加载项目失败", e)
                runOnUiThread {
                    showError("加载项目失败: ${e.message}")
                }
            }
        }
    }

    /**
     * 加载验证过的项目
     */
    private suspend fun loadValidatedProject(project: EnhancedAssetManager.ValidatedProject) {
        // 显示加载状态
        runOnUiThread {
            showLoading()
        }

        try {
            Log.i(TAG, "正在加载项目: ${project.name}")
            Log.i(TAG, "项目信息: ${project.totalRegions}个区域, ${project.totalColors}种颜色, ${project.fileSize.totalSizeKB}KB")

            // 加载填色数据
            val coloringDataResult = enhancedAssetManager.loadColoringData(project.jsonFile)
            if (coloringDataResult.isFailure) {
                throw Exception("加载JSON失败: ${coloringDataResult.exceptionOrNull()?.message}")
            }

            val coloringData = coloringDataResult.getOrNull()!!

            // 加载outline图片
            val outlineBitmapResult = enhancedAssetManager.loadOutlineBitmap(project.outlineFile)
            if (outlineBitmapResult.isFailure) {
                throw Exception("加载outline图片失败: ${outlineBitmapResult.exceptionOrNull()?.message}")
            }

            val outlineBitmap = outlineBitmapResult.getOrNull()!!

            // 在主线程更新UI
            runOnUiThread {
                showReady()
                setupProject(coloringData, outlineBitmap)
                loadSavedProgress(project.name) // 加载保存的进度
                updateUIForProject(project)
                Log.i(TAG, "✅ 项目加载成功: ${project.name}")
            }

        } catch (e: Exception) {
            Log.e(TAG, "加载项目失败: ${project.name}", e)
            runOnUiThread {
                showError("加载项目失败: ${e.message}")
            }
        }
    }

    /**
     * 更新UI以显示项目信息
     */
    private fun updateUIForProject(project: EnhancedAssetManager.ValidatedProject) {
        // 更新标题栏显示项目信息
        supportActionBar?.title = "填色项目: ${project.name}"
        supportActionBar?.subtitle = "${project.totalRegions}区域 • ${project.totalColors}颜色 • ${project.fileSize.totalSizeKB}KB"
    }

    /**
     * 显示项目选择器
     */
    private fun showProjectSelector(projects: List<EnhancedAssetManager.ValidatedProject>) {
        Log.i(TAG, "显示项目选择器: ${projects.size}个可选项目")

        // 显示项目选择对话框
        ProjectSelectionDialog.showProjectSelection(this, projects) { selectedProject ->
            Log.i(TAG, "用户选择了项目: ${selectedProject.name}")

            // 加载选中的项目
            lifecycleScope.launch {
                loadValidatedProject(selectedProject)
            }
        }
    }

    override fun onCreateOptionsMenu(menu: Menu?): Boolean {
        menuInflater.inflate(R.menu.main_menu, menu)
        return true
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        return when (item.itemId) {
            R.id.action_select_project -> {
                showProjectSelectionMenu()
                true
            }
            R.id.action_validation_report -> {
                showValidationReportMenu()
                true
            }
            R.id.action_save_image -> {
                saveCurrentImage()
                true
            }
            R.id.action_reset -> {
                resetProject()
                true
            }
            else -> super.onOptionsItemSelected(item)
        }
    }

    private fun showProjectSelectionMenu() {
        lifecycleScope.launch {
            try {
                val projectsResult = enhancedAssetManager.getValidatedProjects()
                if (projectsResult.isSuccess) {
                    val projects = projectsResult.getOrNull()!!
                    val validProjects = projects.filter { it.isValid }

                    if (validProjects.isNotEmpty()) {
                        ProjectSelectionDialog.showProjectSelection(this@SimpleMainActivity, validProjects) { selectedProject ->
                            lifecycleScope.launch {
                                loadValidatedProject(selectedProject)
                            }
                        }
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "显示项目选择菜单失败", e)
            }
        }
    }

    private fun showValidationReportMenu() {
        lifecycleScope.launch {
            try {
                val projectsResult = enhancedAssetManager.getValidatedProjects()
                if (projectsResult.isSuccess) {
                    val projects = projectsResult.getOrNull()!!
                    ProjectSelectionDialog.showValidationReport(this@SimpleMainActivity, enhancedAssetManager, projects)
                }
            } catch (e: Exception) {
                Log.e(TAG, "显示验证报告失败", e)
            }
        }
    }

    private fun saveCurrentImage() {
        saveCurrentProgress()
    }

    /**
     * 保存当前进度（高效版本，1秒内完成）
     */
    private fun saveCurrentProgress() {
        val coloringData = currentColoringData
        if (coloringData == null) {
            return
        }

        // 获取项目名称，优先使用标准化的名称
        val projectName = getStandardizedProjectName()

        Log.d(TAG, "开始快速保存进度: $projectName")

        // 在后台线程执行保存任务，使用try-catch处理协程取消
        lifecycleScope.launch(Dispatchers.IO) {
            try {
                // 检查协程是否已被取消
                if (!isActive) {
                    Log.d(TAG, "协程已取消，跳过进度保存: $projectName")
                    return@launch
                }

                val startTime = System.currentTimeMillis()

                // 只保存必要的进度数据，不生成预览图片（太耗时）
                val projectSaveManager = com.example.coloringproject.utils.ProjectSaveManager(this@SimpleMainActivity)
                val result = projectSaveManager.saveProgressFast(
                    projectName = projectName,
                    coloringData = coloringData,
                    filledRegions = filledRegions.toSet() // 创建副本避免并发问题
                )

                val endTime = System.currentTimeMillis()
                Log.d(TAG, "快速保存完成，耗时: ${endTime - startTime}ms")

                // 检查协程是否仍然活跃
                if (isActive) {
                    // 在主线程处理结果
                    withContext(Dispatchers.Main) {
                        when (result) {
                            is com.example.coloringproject.utils.SaveResult.Success -> {
                                Log.d(TAG, "Progress saved successfully for project: $projectName")

                                // 通知Library刷新项目进度
                                val progressPercentage = (filledRegions.size * 100) / (coloringData?.regions?.size ?: 1)
                                Log.d(TAG, "异步保存成功，发送进度通知: $projectName, 进度: $progressPercentage%")
                                LibraryEventManager.notifyProjectProgressUpdated(
                                    projectName,
                                    filledRegions.isNotEmpty(),
                                    progressPercentage
                                )
                            }
                            is com.example.coloringproject.utils.SaveResult.Error -> {
                                Log.e(TAG, "Failed to save progress for project: $projectName")
                            }
                        }
                    }
                }
            } catch (e: Exception) {
                handleCoroutineException("进度保存: $projectName", e, isActive)
            }
        }
    }

    // 预览图片生成的延迟任务
    private var previewGenerationRunnable: Runnable? = null
    private val previewGenerationHandler = Handler(Looper.getMainLooper())

    /**
     * 安全的协程异常处理
     */
    private fun handleCoroutineException(operationName: String, exception: Exception, isActive: Boolean = true) {
        if (exception is kotlinx.coroutines.CancellationException || exception.cause is kotlinx.coroutines.CancellationException) {
            Log.d(TAG, "协程操作被取消: $operationName")
        } else if (isActive) {
            Log.e(TAG, "协程操作出错: $operationName", exception)
        }
    }

    /**
     * 调度预览图片生成（延迟执行，避免频繁生成）
     */
    private fun schedulePreviewImageGeneration() {
        // 取消之前的任务
        previewGenerationRunnable?.let { previewGenerationHandler.removeCallbacks(it) }

        // 创建新的延迟任务
        previewGenerationRunnable = Runnable {
            generatePreviewImage()
        }

        // 延迟2秒执行，避免用户连续填色时频繁生成
        previewGenerationHandler.postDelayed(previewGenerationRunnable!!, 2000)

        Log.d(TAG, "已调度预览图片生成任务（2秒后执行）")
    }

    /**
     * 创建适合缩略图显示的bitmap
     */
    private fun createThumbnailBitmap(originalBitmap: Bitmap): Bitmap {
        val maxThumbnailSize = 512 // 最大缩略图尺寸

        val originalWidth = originalBitmap.width
        val originalHeight = originalBitmap.height

        // 如果原图已经足够小，直接返回
        if (originalWidth <= maxThumbnailSize && originalHeight <= maxThumbnailSize) {
            return originalBitmap
        }

        // 计算缩放比例，保持宽高比
        val scale = minOf(
            maxThumbnailSize.toFloat() / originalWidth,
            maxThumbnailSize.toFloat() / originalHeight
        )

        val newWidth = (originalWidth * scale).toInt()
        val newHeight = (originalHeight * scale).toInt()

        // 创建缩放后的bitmap
        val thumbnailBitmap = Bitmap.createScaledBitmap(originalBitmap, newWidth, newHeight, true)

        Log.d(TAG, "创建缩略图: ${originalWidth}x${originalHeight} -> ${newWidth}x${newHeight}")

        return thumbnailBitmap
    }

    /**
     * 生成预览图片（仅在需要时调用，比如手动保存）
     */
    private fun generatePreviewImage() {
        val coloringData = currentColoringData
        if (coloringData == null) {
            return
        }

        val projectName = getStandardizedProjectName()

        lifecycleScope.launch(Dispatchers.IO) {
            try {
                // 检查协程是否已被取消
                if (!isActive) {
                    Log.d(TAG, "协程已取消，跳过预览图片生成: $projectName")
                    return@launch
                }

                // 创建预览图片
                val previewBitmap = withContext(Dispatchers.Main) {
                    if (isActive) {
                        binding.coloringView.captureArtwork(false)
                    } else {
                        null
                    }
                }

                if (isActive && previewBitmap != null) {
                    val projectSaveManager = com.example.coloringproject.utils.ProjectSaveManager(this@SimpleMainActivity)

                    // 生成适合缩略图的尺寸
                    val thumbnailBitmap = createThumbnailBitmap(previewBitmap)
                    projectSaveManager.savePreviewImage(projectName, thumbnailBitmap)

                    // 回收原始bitmap（如果不是同一个对象）
                    if (thumbnailBitmap != previewBitmap) {
                        previewBitmap.recycle()
                    }

                    Log.d(TAG, "Preview image generated for project: $projectName")

                    // 通知Library刷新预览图片
                    val previewPath = projectSaveManager.getPreviewImagePath(projectName)
                    LibraryEventManager.notifyProjectPreviewUpdated(projectName, previewPath)
                }
            } catch (e: Exception) {
                handleCoroutineException("预览图片生成: $projectName", e, isActive)
            }
        }
    }

    /**
     * 异步生成预览图片（用于退出时，不阻塞UI）
     */
    private fun generatePreviewImageAsync() {
        val coloringData = currentColoringData
        if (coloringData == null) {
            return
        }

        val projectName = getStandardizedProjectName()

        // 使用GlobalScope确保即使Activity销毁也能完成
        GlobalScope.launch(Dispatchers.IO) {
            try {
                // 创建预览图片
                val previewBitmap = withContext(Dispatchers.Main) {
                    if (!isDestroyed && !isFinishing) {
                        binding.coloringView.captureArtwork(false)
                    } else {
                        null
                    }
                }

                if (previewBitmap != null) {
                    val projectSaveManager = com.example.coloringproject.utils.ProjectSaveManager(this@SimpleMainActivity)

                    // 生成适合缩略图的尺寸
                    val thumbnailBitmap = createThumbnailBitmap(previewBitmap)
                    projectSaveManager.savePreviewImage(projectName, thumbnailBitmap)

                    // 回收原始bitmap（如果不是同一个对象）
                    if (thumbnailBitmap != previewBitmap) {
                        previewBitmap.recycle()
                    }

                    Log.d(TAG, "Async preview image generated for project: $projectName")

                    // 通知Library刷新预览图片
                    val previewPath = projectSaveManager.getPreviewImagePath(projectName)
                    LibraryEventManager.notifyProjectPreviewUpdated(projectName, previewPath)
                }
            } catch (e: Exception) {
                handleCoroutineException("异步预览图片生成: $projectName", e)
            }
        }
    }

    /**
     * 同步保存当前进度 - 用于退出前确保保存完成
     */
    private suspend fun saveCurrentProgressSync() = withContext(Dispatchers.IO) {
        val projectName = getStandardizedProjectName()
        val coloringData = currentColoringData ?: return@withContext

        val startTime = System.currentTimeMillis()

        // 同步保存进度数据
        val projectSaveManager = com.example.coloringproject.utils.ProjectSaveManager(this@SimpleMainActivity)
        val result = projectSaveManager.saveProgressFast(
            projectName = projectName,
            coloringData = coloringData,
            filledRegions = filledRegions.toSet()
        )

        val endTime = System.currentTimeMillis()
        Log.d(TAG, "同步保存完成，耗时: ${endTime - startTime}ms")

        // 在主线程处理结果
        withContext(Dispatchers.Main) {
            when (result) {
                is com.example.coloringproject.utils.SaveResult.Success -> {
                    Log.d(TAG, "Progress saved successfully for project: $projectName")

                    // 通知Library刷新项目进度
                    val progressPercentage = (filledRegions.size * 100) / (coloringData.regions.size)
                    Log.d(TAG, "同步保存成功，发送进度通知: $projectName, 进度: $progressPercentage%")
                    LibraryEventManager.notifyProjectProgressUpdated(
                        projectName,
                        filledRegions.isNotEmpty(),
                        progressPercentage
                    )
                }
                is com.example.coloringproject.utils.SaveResult.Error -> {
                    Log.e(TAG, "Failed to save progress for project: $projectName")
                }
            }
        }
    }

    /**
     * 同步生成预览图片 - 用于退出前确保缩略图更新
     */
    private suspend fun generatePreviewImageSync() = withContext(Dispatchers.IO) {
        val projectName = getStandardizedProjectName()
        val coloringData = currentColoringData ?: return@withContext

        try {
            val startTime = System.currentTimeMillis()

            // 在主线程生成预览图片
            val previewBitmap = withContext(Dispatchers.Main) {
                if (!isDestroyed && !isFinishing) {
                    binding.coloringView.captureArtwork(false)
                } else {
                    null
                }
            }

            val generateTime = System.currentTimeMillis() - startTime
            Log.d(TAG, "同步预览图片生成耗时: ${generateTime}ms")

            if (previewBitmap != null) {
                val projectSaveManager = com.example.coloringproject.utils.ProjectSaveManager(this@SimpleMainActivity)

                // 生成适合缩略图的尺寸
                val thumbnailBitmap = createThumbnailBitmap(previewBitmap)
                val saveResult = projectSaveManager.savePreviewImage(projectName, thumbnailBitmap)

                // 回收原始bitmap（如果不是同一个对象）
                if (thumbnailBitmap != previewBitmap) {
                    previewBitmap.recycle()
                }

                Log.d(TAG, "同步预览图片保存完成: $projectName")

                // 在主线程通知Library刷新预览图片
                withContext(Dispatchers.Main) {
                    val previewPath = projectSaveManager.getPreviewImagePath(projectName)
                    LibraryEventManager.notifyProjectPreviewUpdated(projectName, previewPath)
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "同步预览图片生成失败: $projectName", e)
        }
    }

    /**
     * 获取标准化的项目名称（用于保存和加载）
     * 使用统一的命名规则
     */
    private fun getStandardizedProjectName(): String {
        return ProjectNameUtils.getUnifiedProjectId(intent = intent)
    }

    /**
     * 加载保存的进度
     */
    private fun loadSavedProgress(projectName: String) {
        Log.d(TAG, "=== 开始加载进度 ===")
        Log.d(TAG, "项目名称: $projectName")

        val projectSaveManager = com.example.coloringproject.utils.ProjectSaveManager(this)

        // 检查进度文件是否存在
        val progressFile = java.io.File(getExternalFilesDir(null), "progress/${projectName}_progress.json")
        Log.d(TAG, "进度文件路径: ${progressFile.absolutePath}")
        Log.d(TAG, "进度文件存在: ${progressFile.exists()}")

        if (progressFile.exists()) {
            Log.d(TAG, "进度文件大小: ${progressFile.length()} bytes")
        }

        val result = projectSaveManager.loadFullProgress(projectName)

        when (result) {
            is com.example.coloringproject.utils.LoadResult.Success -> {
                val fullProgressData = result.data

                // 使用安全的进度恢复方法
                val restoreSuccess = binding.coloringView.restoreProgressSafely(fullProgressData.filledRegions)

                if (restoreSuccess) {
                    // 获取ColoringView中实际恢复的填色区域
                    val actualFilledRegions = binding.coloringView.getFilledRegions()

                    // 更新本地填色区域集合
                    filledRegions.clear()
                    filledRegions.addAll(actualFilledRegions)

                    // 使用实际的填色区域数据更新进度显示
                    updateProgress()
                    updateAllColorProgress(actualFilledRegions) // 使用实际恢复的填色区域

                    // 验证恢复后的状态
                    val validationResult = binding.coloringView.validateCurrentState()
                    if (!validationResult.isValid) {
                        Log.w(TAG, "State validation after restore: ${validationResult.message}")
                    }

                    // 使用验证工具进行详细验证
                    currentColoringData?.let { data ->
                        val progressValidator = com.example.coloringproject.utils.ProgressRestoreValidator
                        val colorMatchingResult = progressValidator.validateColorMatching(actualFilledRegions, data)
                        Log.d(TAG, "Color matching validation: ${colorMatchingResult.completedColors.size} colors completed")
                    }

                    Log.d(TAG, "Progress safely restored for project: $projectName, filled regions: ${actualFilledRegions.size}")

                    // 记录数据一致性检查
                    if (fullProgressData.filledRegions.size != actualFilledRegions.size) {
                        val originalCount = fullProgressData.filledRegions.size
                        val actualCount = actualFilledRegions.size
                        Log.d(TAG, "Progress data filtered: $originalCount -> $actualCount regions")
                    }

                    // 显示恢复成功提示
                    if (fullProgressData.filledRegions.size != filledRegions.size) {
                        val filteredCount = fullProgressData.filledRegions.size - filledRegions.size
                        android.widget.Toast.makeText(
                            this,
                            "进度已恢复，过滤了 $filteredCount 个无效区域",
                            android.widget.Toast.LENGTH_SHORT
                        ).show()
                    }
                } else {
                    Log.w(TAG, "Failed to restore progress for project: $projectName")
                    android.widget.Toast.makeText(
                        this,
                        "进度恢复失败，将从头开始",
                        android.widget.Toast.LENGTH_SHORT
                    ).show()
                }
            }
            is com.example.coloringproject.utils.LoadResult.Error -> {
                Log.d(TAG, "No saved progress found for project: $projectName")
                // 没有保存的进度，这是正常情况，不需要显示错误
            }
        }
    }


    private fun setupProject(coloringData: ColoringData, bitmap: android.graphics.Bitmap) {
        val setupStart = System.currentTimeMillis()
        Log.d("SimpleMainActivity", "🔧 [性能] Setting up project - 开始")

        // 步骤1：基础数据设置
        val step1Start = System.currentTimeMillis()
        currentColoringData = coloringData
        filledRegions.clear()
        val step1Time = System.currentTimeMillis() - step1Start
        Log.d("SimpleMainActivity", "📊 [性能] 基础数据设置耗时: ${step1Time}ms")

        // 步骤2：ColoringView设置
        val step2Start = System.currentTimeMillis()
        binding.coloringView.setColoringData(coloringData, bitmap)
        val step2Time = System.currentTimeMillis() - step2Start
        Log.d("SimpleMainActivity", "📊 [性能] ColoringView设置耗时: ${step2Time}ms")

        // 设置ColoringView回调
        binding.coloringView.onRegionTouched = { result ->
            handleRegionTouch(result)
        }

        binding.coloringView.onProjectCompleted = {
            // 整个项目完成时的处理
            runOnUiThread {
                showCompletionDialog()
            }
        }

        binding.coloringView.onColorCompleted = { colorHex, regionCount ->
            // 某种颜色全部完成时的处理
            runOnUiThread {

                // 更新颜色进度显示，触发自动切换逻辑
                updateColorProgress()
            }
        }

        binding.coloringView.onColorAutoSelected = { colorHex ->
            // 长按自动选择颜色时的处理
            runOnUiThread {
                autoSelectColorByHex(colorHex)
            }
        }

        // 设置提醒状态 - 确保马赛克提醒开启
        hintsEnabled = true
        binding.coloringView.setShowHints(hintsEnabled)
        binding.coloringView.setHintAlpha(0.4f) // 设置适中的透明度

        // 步骤3：处理调色板（最耗时的操作）
        val step3Start = System.currentTimeMillis()
        cachedProcessedPalette = processColorPaletteWithProgress(coloringData.colorPalette, coloringData.regions)
        val step3Time = System.currentTimeMillis() - step3Start
        Log.d("SimpleMainActivity", "📊 [性能] 调色板处理耗时: ${step3Time}ms")

        // 步骤4：设置UI组件
        val step4Start = System.currentTimeMillis()
        val colorInfoList = convertToColorInfo(cachedProcessedPalette)
        immersiveColorAdapter.updateColors(colorInfoList, preserveProgress = false)
        val step4Time = System.currentTimeMillis() - step4Start
        Log.d("SimpleMainActivity", "📊 [性能] 颜色适配器更新耗时: ${step4Time}ms")

        // 步骤5：初始化颜色进度
        val step5Start = System.currentTimeMillis()
        updateAllColorProgress()
        val step5Time = System.currentTimeMillis() - step5Start
        Log.d("SimpleMainActivity", "📊 [性能] 颜色进度初始化耗时: ${step5Time}ms")

        // 选择第一个颜色
        if (cachedProcessedPalette.isNotEmpty()) {
            selectColor(cachedProcessedPalette.first())
        }

        // 更新定位按钮状态
//        binding.btnToggleHints.src = "定位"
        
        // 更新进度
        updateProgress()
        
        // 显示项目信息
        binding.tvStatus.text = "项目已加载: ${coloringData.metadata.totalRegions}个区域"

        // 步骤6：显示UI
        val step6Start = System.currentTimeMillis()
        showReady()
        showColoringUI() // 显示颜色面板和其他UI控件
        val step6Time = System.currentTimeMillis() - step6Start
        Log.d("SimpleMainActivity", "📊 [性能] UI显示耗时: ${step6Time}ms")

        val totalTime = System.currentTimeMillis() - setupStart
        Log.d("SimpleMainActivity", "🎯 [性能] 项目设置总耗时: ${totalTime}ms")
        Log.d("SimpleMainActivity", "✅ Project setup completed")
    }

    /**
     * 快速设置项目（用于Gallery加载，跳过重复初始化）
     */
    private fun setupProjectFast(coloringData: ColoringData, outlineBitmap: Bitmap, savedFilledRegions: Set<Int>) {
        val setupStart = System.currentTimeMillis()
        Log.d(TAG, "🔧 [性能] Fast project setup started")

        // 步骤1：设置基础数据
        val step1Start = System.currentTimeMillis()
        currentColoringData = coloringData
        binding.coloringView.setColoringData(coloringData, outlineBitmap)
        val step1Time = System.currentTimeMillis() - step1Start
        Log.d(TAG, "📊 [性能] 设置ColoringView耗时: ${step1Time}ms")

        // 步骤2：安全恢复填色状态
        val step2Start = System.currentTimeMillis()
        val restoreSuccess = binding.coloringView.restoreProgressSafely(savedFilledRegions)
        val actualFilledRegions = if (restoreSuccess) {
            val restored = binding.coloringView.getFilledRegions()
            filledRegions.clear()
            filledRegions.addAll(restored)
            restored
        } else {
            Log.w(TAG, "Fast setup: Failed to restore progress, starting fresh")
            filledRegions.clear()
            emptySet<Int>()
        }
        val step2Time = System.currentTimeMillis() - step2Start
        Log.d(TAG, "📊 [性能] 安全恢复填色状态耗时: ${step2Time}ms")

        // 🚀 优化：简化调色板初始化，不计算所有颜色进度
        val step3Start = System.currentTimeMillis()
        // 使用简化的调色板处理，不预计算进度
        cachedProcessedPalette = processColorPaletteSimple(coloringData.colorPalette)
        val colorInfoList = convertToColorInfo(cachedProcessedPalette)

        // 设置适配器为只显示选中颜色进度模式
        immersiveColorAdapter.setShowOnlySelectedProgress(true)

        // 更新颜色列表
        immersiveColorAdapter.updateColors(colorInfoList, preserveProgress = false)

        Log.d(TAG, "🚀 [性能优化] 跳过全量进度计算，只在颜色选择时计算单个进度")

        val step3Time = System.currentTimeMillis() - step3Start
        Log.d(TAG, "📊 [性能] 设置调色板和进度耗时: ${step3Time}ms")

        // 验证进度设置是否正确
        validateAdapterProgress()

        // 强制刷新适配器，解决视图复用问题
        immersiveColorAdapter.forceRefreshAll()



        // 步骤5：选择颜色
        val step5Start = System.currentTimeMillis()
        val firstUnfinishedColor = cachedProcessedPalette.find { palette ->
            val colorRegions = coloringData.regions.filter { region ->
                normalizeColorHex(region.colorHex) == normalizeColorHex(palette.colorHex)
            }
            val filledCount = colorRegions.count { region ->
                savedFilledRegions.contains(region.id)
            }
            filledCount < colorRegions.size
        }

        if (firstUnfinishedColor != null) {
            selectColor(firstUnfinishedColor)
        } else if (cachedProcessedPalette.isNotEmpty()) {
            selectColor(cachedProcessedPalette.first())
        }
        val step5Time = System.currentTimeMillis() - step5Start
        Log.d(TAG, "📊 [性能] 选择颜色耗时: ${step5Time}ms")

        // 步骤6：显示UI
        val step6Start = System.currentTimeMillis()
        binding.tvStatus.text = "项目已加载: ${coloringData.metadata.totalRegions}个区域"
        showReady()
        showColoringUI()
        val step6Time = System.currentTimeMillis() - step6Start
        Log.d(TAG, "📊 [性能] 显示UI耗时: ${step6Time}ms")

        val totalSetupTime = System.currentTimeMillis() - setupStart
        Log.d(TAG, "🔧 [性能] Fast project setup completed, 总耗时: ${totalSetupTime}ms")

        // 性能分析
        if (totalSetupTime > 100) {
            Log.w(TAG, "⚠️ [性能] setupProjectFast超过100ms，需要优化")
        }
    }

    /**
     * 🚀 优化：移除全量颜色进度更新，改为按需更新
     * 只在需要时更新当前选中颜色的进度
     */
    private fun updateSelectedColorProgressOnly(savedFilledRegions: Set<Int>) {
        // 只更新当前选中颜色的进度
        updateCurrentSelectedColorProgress()

        // 更新总进度
        updateProgress()

        Log.d(TAG, "🚀 [性能优化] 跳过全量颜色进度计算，只更新选中颜色")
    }
    
    /**
     * 处理调色板，去重同色并计算进度
     * @param originalPalette 原始调色板
     * @param regions 区域列表
     * @param customFilledRegions 自定义的已填色区域集合，如果为null则使用全局filledRegions
     */
    private fun processColorPaletteWithProgress(
        originalPalette: List<ColorPalette>,
        regions: List<Region>,
        customFilledRegions: Set<Int>? = null
    ): List<ColorPalette> {
        // 使用传入的填色区域数据，如果没有则使用全局数据
        val targetFilledRegions = customFilledRegions ?: filledRegions

        // 按标准化颜色分组区域
        val regionsByColor = regions.groupBy { region ->
            normalizeColorHex(region.colorHex)
        }

        // 去重颜色并计算进度
        val uniqueColors = mutableMapOf<String, ColorPalette>()

        for (palette in originalPalette) {
            val normalizedColorHex = normalizeColorHex(palette.colorHex)

            if (!uniqueColors.containsKey(normalizedColorHex)) {
                // 计算这种颜色的区域总数
                val colorRegions = regionsByColor[normalizedColorHex] ?: emptyList()
                val totalCount = colorRegions.size

                // 计算已填充的区域数
                val filledCount = colorRegions.count { region ->
                    targetFilledRegions.contains(region.id)
                }

                // 创建带进度信息的调色板项
                val paletteWithProgress = palette.copy(
                    filledCount = filledCount,
                    totalCount = totalCount
                )

                uniqueColors[normalizedColorHex] = paletteWithProgress

                Log.d("SimpleMainActivity", "Color ${palette.name} ($normalizedColorHex): $filledCount/$totalCount (using ${if (customFilledRegions != null) "custom" else "global"} filled regions)")
            }
        }

        return uniqueColors.values.toList().sortedBy { it.id }
    }

    private fun selectColor(color: ColorPalette) {
        Log.d("SimpleMainActivity", "Selecting color: ${color.colorHex} (${color.name})")

        currentSelectedColor = color

        // 找到处理后的调色板中的索引（使用缓存）
        val colorIndex = cachedProcessedPalette.indexOfFirst {
            normalizeColorHex(it.colorHex) == normalizeColorHex(color.colorHex)
        }
        if (colorIndex != -1) {
            // 更新沉浸式适配器的选中状态
            val colorInfo = ColorInfo(
                id = colorIndex,
                name = color.name,
                hexColor = color.colorHex
            )
            immersiveColorAdapter.setSelectedColor(colorInfo)
            Log.d(TAG, "选中颜色索引: $colorIndex, 颜色: ${color.name}")
        } else {
            Log.w(TAG, "未找到颜色索引: ${color.colorHex}")
        }

        // 设置当前颜色到ColoringView，这会触发马赛克显示
        binding.coloringView.setCurrentColor(color.colorHex)

        // 更新当前颜色显示
        val drawable = GradientDrawable().apply {
            shape = GradientDrawable.OVAL
            setColor(Color.parseColor(color.colorHex))
            setStroke(4, Color.GRAY)
        }
        binding.currentColorView.background = drawable

        // 🚀 优化：计算并显示当前选中颜色的进度
        updateCurrentColorDisplay(color)
        updateCurrentSelectedColorProgress() // 按需计算当前颜色进度

        // 确保马赛克提醒开启
        binding.coloringView.setShowHints(true)

        Log.d("SimpleMainActivity", "🚀 [性能优化] 颜色选择完成，已计算当前颜色进度: ${color.colorHex}")
    }

    /**
     * 标准化颜色十六进制格式
     */
    private fun normalizeColorHex(colorHex: String): String {
        var normalized = colorHex.trim().lowercase()
        if (!normalized.startsWith("#")) {
            normalized = "#$normalized"
        }
        // 确保是6位十六进制格式
        if (normalized.length == 4) {
            // #RGB -> #RRGGBB
            val r = normalized[1]
            val g = normalized[2]
            val b = normalized[3]
            normalized = "#$r$r$g$g$b$b"
        }
        return normalized
    }

    /**
     * 更新当前颜色的显示信息
     */
    private fun updateCurrentColorDisplay(color: ColorPalette) {
        val coloringData = currentColoringData ?: return

        val normalizedColorHex = normalizeColorHex(color.colorHex)
        val colorRegions = coloringData.regions.filter { region ->
            normalizeColorHex(region.colorHex) == normalizedColorHex
        }
        val filledColorRegions = colorRegions.filter { region ->
            filledRegions.contains(region.id)
        }

        val progressText = "${filledColorRegions.size}/${colorRegions.size}"
        binding.tvCurrentColorName.text = "${color.name} ($progressText)"

        Log.d("SimpleMainActivity", "Updated current color display: ${color.name} = $progressText (normalized: $normalizedColorHex)")
    }
    
    private fun selectNextColor() {
        val currentData = currentColoringData ?: return
        val currentColor = currentSelectedColor ?: return
        
        val currentIndex = currentData.colorPalette.indexOf(currentColor)
        val nextIndex = (currentIndex + 1) % currentData.colorPalette.size
        selectColor(currentData.colorPalette[nextIndex])
    }
    
    private fun handleRegionTouch(result: TouchResult) {
        try {
            if (result.isValidTouch && result.regionId != null) {
                // 立即更新UI反馈
                filledRegions.add(result.regionId)
                updateProgress()
                
                // 在后台线程处理耗时操作
                lifecycleScope.launch {
                    try {
                        // 后台处理颜色进度计算
                        val colorProgressResult = withContext(Dispatchers.Default) {
                            calculateColorProgress()
                        }
                        
                        // 主线程更新UI
                        withContext(Dispatchers.Main) {
                            if (isActive && !isDestroyed) {
                                updateColorProgressUI(colorProgressResult)
                                
                                // 延迟生成预览图片（避免频繁生成）
                                schedulePreviewImageGeneration()
                            }
                        }
                    } catch (e: Exception) {
                        Log.e(TAG, "Error processing region touch", e)
                        withContext(Dispatchers.Main) {
                            if (isActive && !isDestroyed) {
                                showError("处理填色操作时出错")
                            }
                        }
                    }
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error in handleRegionTouch", e)
            showError("填色操作失败，请重试")
        }
    }
    
    /**
     * 在后台线程计算颜色进度
     */
    private fun calculateColorProgress(): Map<String, Pair<Int, Int>> {
        val data = currentColoringData ?: return emptyMap()
        val colorStats = mutableMapOf<String, Pair<Int, Int>>()
        
        // 按颜色分组计算进度
        data.regions.groupBy { normalizeColorHex(it.colorHex) }.forEach { (color, regions) ->
            val filledCount = regions.count { filledRegions.contains(it.id) }
            colorStats[color] = Pair(filledCount, regions.size)
        }
        
        return colorStats
    }
    
    /**
     * 在主线程更新颜色进度UI
     */
    private fun updateColorProgressUI(colorStats: Map<String, Pair<Int, Int>>) {
        try {
            updateCurrentSelectedColorProgress()
            
            // 如果需要更新所有颜色进度，可以在这里处理
            // updateAllColorProgressFromStats(colorStats)
        } catch (e: Exception) {
            Log.e(TAG, "Error updating color progress UI", e)
        }
    }
    
    private fun updateProgress() {
        try {
            val totalRegions = currentColoringData?.metadata?.totalRegions ?: 0
            val filled = filledRegions.size
            binding.tvProgress.text = "$filled / $totalRegions"
            
            val progressPercentage = if (totalRegions > 0) (filled * 100 / totalRegions) else 0
            binding.progressBar.progress = progressPercentage
            
            Log.d("SimpleMainActivity", "updateProgress: $progressPercentage% ($filled/$totalRegions)")
        } catch (e: Exception) {
            Log.e("SimpleMainActivity", "Error updating progress", e)
            // 设置默认值，防止崩溃
            binding.tvProgress.text = "0 / 0"
            binding.progressBar.progress = 0
        }
    }

    /**
     * 更新颜色进度显示（优化版：只更新当前选中颜色）
     */
    private fun updateColorProgress() {
        updateCurrentSelectedColorProgress()
    }

    /**
     * 更新所有颜色进度显示
     * @param customFilledRegions 自定义的已填色区域集合，如果为null则使用全局filledRegions
     */
    private fun updateAllColorProgress(customFilledRegions: Set<Int>? = null) {
        val targetFilledRegions = customFilledRegions ?: filledRegions
        Log.d(TAG, "updateAllColorProgress() called with ${targetFilledRegions.size} filled regions (${if (customFilledRegions != null) "custom" else "global"})")

        currentColoringData?.let { data ->
            // 重新计算所有颜色的进度，使用指定的填色区域数据
            val processedColorPalette = processColorPaletteWithProgress(data.colorPalette, data.regions, customFilledRegions)

            Log.d(TAG, "Processed palette size: ${processedColorPalette.size}")

            // 详细调试：检查处理后的调色板
            processedColorPalette.forEachIndexed { index, palette ->
                Log.d(TAG, "调色板项 $index: ${palette.name} (${palette.colorHex}) - ${palette.filledCount}/${palette.totalCount}")
            }

            // 直接更新每个颜色的进度到适配器，使用正确的ID映射
            processedColorPalette.forEachIndexed { index, palette ->
                val progress = if (palette.totalCount > 0) {
                    (palette.filledCount * 100) / palette.totalCount
                } else {
                    0
                }

                Log.d(TAG, "准备更新颜色进度: ${palette.name} (index=$index, colorHex=${palette.colorHex}) = $progress% (${palette.filledCount}/${palette.totalCount})")

                // 使用index作为colorId，因为convertToColorInfo中使用的是index作为id
                immersiveColorAdapter.updateColorProgress(index, progress)

                Log.d(TAG, "已更新颜色进度: ${palette.name} (index=$index) = $progress%")
            }

            // 验证进度更新的正确性
            validateColorProgressConsistency(targetFilledRegions)

        }
    }

    /**
     * 验证颜色进度的一致性
     */
    private fun validateColorProgressConsistency(filledRegions: Set<Int>) {
        currentColoringData?.let { data ->
            val totalRegions = data.regions.size
            val actualFilledCount = filledRegions.size
            val expectedProgress = if (totalRegions > 0) (actualFilledCount * 100) / totalRegions else 0

            Log.d(TAG, "Progress validation: $actualFilledCount/$totalRegions regions filled ($expectedProgress%)")

            // 验证每种颜色的进度
            val colorStats = mutableMapOf<String, Pair<Int, Int>>() // 颜色 -> (已填充, 总数)
            data.regions.groupBy { normalizeColorHex(it.colorHex) }.forEach { (color, regions) ->
                val filledCount = regions.count { filledRegions.contains(it.id) }
                colorStats[color] = Pair(filledCount, regions.size)
            }

            Log.d(TAG, "Color progress validation: ${colorStats.size} unique colors")
            colorStats.forEach { (color, stats) ->
                val progress = if (stats.second > 0) (stats.first * 100) / stats.second else 0
                Log.d(TAG, "Color $color: ${stats.first}/${stats.second} = $progress%")
            }
        }
    }

    /**
     * 验证适配器中的进度设置是否正确
     */
    private fun validateAdapterProgress() {
        Log.d(TAG, "=== 验证适配器进度 ===")

        // 获取适配器中的实际进度
        val adapterProgress = immersiveColorAdapter.getProgressStatus()

        cachedProcessedPalette.forEachIndexed { index, palette ->
            val expectedProgress = if (palette.totalCount > 0) {
                (palette.filledCount * 100) / palette.totalCount
            } else {
                0
            }

            val actualProgress = adapterProgress[index] ?: -1
            val isCorrect = actualProgress == expectedProgress

            Log.d(TAG, "颜色 $index (${palette.name}): 期望 $expectedProgress%, 实际 $actualProgress% ${if (isCorrect) "✓" else "✗"}")

            if (!isCorrect) {
                Log.w(TAG, "进度不匹配！颜色 ${palette.name} 期望 $expectedProgress% 但实际是 $actualProgress%")
            }
        }

        // 调用适配器的调试方法
        immersiveColorAdapter.debugProgressStatus()

        Log.d(TAG, "=== 适配器进度验证完成 ===")
    }

    /**
     * 显示项目加载错误对话框
     */
    private fun showProjectLoadError(title: String, message: String) {
        Log.e(TAG, "项目加载错误: $title - $message")

        AlertDialog.Builder(this)
            .setTitle("⚠️ $title")
            .setMessage(message)
            .setPositiveButton("确定") { _, _ ->
                // 用户确认后不做额外操作，已经会加载默认项目
            }
            .setNeutralButton("重试") { _, _ ->
                // 提供重试选项
                val projectId = intent.getStringExtra("project_id")
                val projectSource = intent.getStringExtra("project_source")
                if (projectId != null) {
                    Log.i(TAG, "用户选择重试加载项目: $projectId")
                    loadProjectById(projectId, projectSource)
                } else {
                    loadFirstValidatedProject()
                }
            }
            .setCancelable(false)
            .show()
    }

    /**
     * 根据颜色十六进制值自动选择颜色
     */
    private fun autoSelectColorByHex(colorHex: String) {
        Log.d(TAG, "自动选择颜色: $colorHex")

        // 标准化颜色值
        val normalizedColorHex = normalizeColorHex(colorHex)

        // 检查是否是背景色或空白区域（白色、透明等）
        if (isBackgroundColor(normalizedColorHex)) {
            Log.d(TAG, "长按的是背景区域，不进行颜色选择")
            return
        }

        // 在缓存的调色板中查找匹配的颜色
        val matchingColor = cachedProcessedPalette.find { palette ->
            normalizeColorHex(palette.colorHex) == normalizedColorHex
        }

        if (matchingColor != null) {
            // 找到匹配的颜色，选择它
            selectColor(matchingColor)

            // 显示提示信息
            Toast.makeText(this, "已选择颜色: ${matchingColor.name}", Toast.LENGTH_SHORT).show()

            Log.d(TAG, "成功自动选择颜色: ${matchingColor.name} ($normalizedColorHex)")
        } else {
            // 没有找到匹配的颜色，但不显示错误提示，因为可能是用户误触
            Log.d(TAG, "长按区域颜色不在调色板中: $normalizedColorHex")
        }
    }

    /**
     * 检查是否是背景色
     */
    private fun isBackgroundColor(normalizedColorHex: String): Boolean {
        val backgroundColors = setOf(
            "#FFFFFF", // 白色
            "#000000", // 黑色
            "#00FFFFFF", // 透明白色
            "#00000000", // 完全透明
            "#FFFFFFFF", // 不透明白色
            "#FF000000"  // 不透明黑色
        )
        return backgroundColors.contains(normalizedColorHex.uppercase())
    }

    /**
     * 🚀 优化：更新当前选中颜色的进度（按需计算）
     */
    private fun updateCurrentSelectedColorProgress() {
        val currentColor = currentSelectedColor ?: return
        val data = currentColoringData ?: return

        val startTime = System.currentTimeMillis()

        // 只计算当前选中颜色的进度
        val normalizedColorHex = normalizeColorHex(currentColor.colorHex)

        // 找到这种颜色的所有区域
        val colorRegions = data.regions.filter { region ->
            normalizeColorHex(region.colorHex) == normalizedColorHex
        }

        // 计算已填充的区域数
        val filledCount = colorRegions.count { region ->
            filledRegions.contains(region.id)
        }

        val totalCount = colorRegions.size
        val progress = if (totalCount > 0) {
            (filledCount * 100) / totalCount
        } else {
            0
        }

        // 找到当前颜色在缓存的调色板中的索引
        val colorIndex = cachedProcessedPalette.indexOfFirst { palette ->
            normalizeColorHex(palette.colorHex) == normalizedColorHex
        }

        if (colorIndex >= 0) {
            immersiveColorAdapter.updateColorProgress(colorIndex, progress)

            val calcTime = System.currentTimeMillis() - startTime
            Log.d(TAG, "🚀 [性能优化] 单颜色进度计算耗时: ${calcTime}ms")
            Log.d(TAG, "更新当前选中颜色进度: ${currentColor.name} (index=$colorIndex) = $progress% ($filledCount/$totalCount)")
        }

        // 检查当前颜色是否完成，如果完成则自动切换
        if (progress == 100) {
            checkAndSwitchToNextColor()
        }
    }

    /**
     * 检查并切换到下一个未完成的颜色
     */
    private fun checkAndSwitchToNextColor() {
        val data = currentColoringData ?: return

        // 使用缓存的调色板，避免重复计算
        val nextColor = cachedProcessedPalette.find { palette ->
            val normalizedColorHex = normalizeColorHex(palette.colorHex)
            val colorRegions = data.regions.filter { region ->
                normalizeColorHex(region.colorHex) == normalizedColorHex
            }
            val filledCount = colorRegions.count { region ->
                filledRegions.contains(region.id)
            }
            filledCount < colorRegions.size // 找到未完成的颜色
        }

        if (nextColor != null) {
            selectColor(nextColor)
            Log.d(TAG, "自动切换到下一个颜色: ${nextColor.name}")
        } else {
            Log.d(TAG, "所有颜色都已完成！")
        }
    }

    /**
     * 找到颜色在适配器中的索引
     */
    private fun findColorIndexInAdapter(targetColor: ColorPalette): Int {
        val data = currentColoringData ?: return -1
        val processedPalette = processColorPaletteWithProgress(data.colorPalette, data.regions)

        val targetNormalizedHex = normalizeColorHex(targetColor.colorHex)
        return processedPalette.indexOfFirst { palette ->
            normalizeColorHex(palette.colorHex) == targetNormalizedHex
        }
    }
    
    private fun resetProject() {
        filledRegions.clear()
        binding.coloringView.resetColoring()
        updateProgress()
        updateAllColorProgress()  // 重置所有颜色进度
    }

    /**
     * 定位到当前颜色的待填色区域
     */
    private fun focusOnCurrentColorRegion() {
        val currentColor = currentSelectedColor
        val coloringData = currentColoringData

        if (currentColor == null || coloringData == null) {
            return
        }

        // 找到当前颜色的未填色区域
        val unfilledRegions = coloringData.regions.filter { region ->
            region.colorHex.equals(currentColor.colorHex, ignoreCase = true) &&
            !filledRegions.contains(region.id)
        }

        if (unfilledRegions.isEmpty()) {
            return
        }

        // 选择第一个区域进行定位
        val targetRegion = unfilledRegions.first()

        // 定位到该区域
        binding.coloringView.focusOnRegion(targetRegion)


    }

    private fun toggleHints() {
        hintsEnabled = !hintsEnabled
        binding.coloringView.setShowHints(hintsEnabled)

        // 更新按钮文本
//        binding.btnToggleHints.text = if (hintsEnabled) "定位" else "定位"


    }
    
    private fun showLoading() {
        binding.loadingIndicator.visibility = View.VISIBLE
        binding.tvStatus.visibility = View.GONE
    }

    private fun showReady() {
        binding.loadingIndicator.visibility = View.GONE
        binding.tvStatus.visibility = View.GONE
    }

    private fun showError(message: String) {
        binding.loadingIndicator.visibility = View.GONE
        binding.tvStatus.visibility = View.VISIBLE
        binding.tvStatus.text = message
    }
    
    private fun showProjectSelectionDialog() {
        lifecycleScope.launch {
            try {
                Log.i(TAG, "显示项目选择对话框...")

                val projectsResult = enhancedAssetManager.getValidatedProjects()
                if (projectsResult.isFailure) {
                    Log.e(TAG, "项目验证失败", projectsResult.exceptionOrNull())
                    runOnUiThread {
                    }
                    return@launch
                }

                val projects = projectsResult.getOrNull()!!
                val validProjects = projects.filter { it.isValid }

                Log.i(TAG, "项目验证完成: 总计 ${projects.size} 个项目, 有效 ${validProjects.size} 个")

                runOnUiThread {
                    if (validProjects.isEmpty()) {
                        // 显示验证报告
                        val report = enhancedAssetManager.getValidationReport(projects)
                        AlertDialog.Builder(this@SimpleMainActivity)
                            .setTitle("❌ 没有有效项目")
                            .setMessage("没有找到有效的填色项目\n\n$report")
                            .setPositiveButton("确定", null)
                            .show()
                        return@runOnUiThread
                    }

                    // 使用新的项目选择对话框
                    ProjectSelectionDialog.showProjectSelection(this@SimpleMainActivity, validProjects) { selectedProject ->
                        Log.i(TAG, "用户选择了项目: ${selectedProject.name}")

                        // 加载选中的项目
                        lifecycleScope.launch {
                            loadValidatedProject(selectedProject)
                        }
                    }
                }

            } catch (e: Exception) {
                Log.e(TAG, "显示项目选择对话框失败", e)
                runOnUiThread {
                }
            }
        }
    }
    

    
    private fun showCompletionDialog() {
        val projectName = getStandardizedProjectName()
        Log.d(TAG, "项目完成: $projectName")

        // 立即保存完成状态和生成预览图
        lifecycleScope.launch {
            try {
                // 1. 立即保存进度
                saveCurrentProgressSync()

                // 2. 立即生成预览图
                generatePreviewImageSync()

                // 3. 发送完成通知
                withContext(Dispatchers.Main) {
                    LibraryEventManager.notifyProjectCompleted(projectName)

                    // 发送进度和预览图更新通知
                    val progressPercentage = 100 // 完成时进度为100%
                    LibraryEventManager.notifyProjectProgressUpdated(projectName, true, progressPercentage)

                    val projectSaveManager = com.example.coloringproject.utils.ProjectSaveManager(this@SimpleMainActivity)
                    val previewPath = projectSaveManager.getPreviewImagePath(projectName)
                    LibraryEventManager.notifyProjectPreviewUpdated(projectName, previewPath)

                    Log.d(TAG, "项目完成通知已发送: $projectName")
                }
            } catch (e: Exception) {
                Log.e(TAG, "项目完成保存失败: $projectName", e)
            }
        }

        AlertDialog.Builder(this)
            .setTitle("🎉 恭喜完成！")
            .setMessage("您已完成所有区域的填色！")
            .setCancelable(true)
            .show()
    }

    /**
     * 检查区域完整性
     */
    private fun checkRegionCompleteness(data: ColoringData) {
        Log.d("SimpleMainActivity", "=== 区域完整性检查 ===")
        Log.d("SimpleMainActivity", "总区域数: ${data.metadata.totalRegions}")
        Log.d("SimpleMainActivity", "实际区域数: ${data.regions.size}")

        // 检查每种颜色的区域数量
        val colorRegionCount = mutableMapOf<String, Int>()
        data.regions.forEach { region ->
            val normalizedColor = region.colorHex.lowercase()
            colorRegionCount[normalizedColor] = (colorRegionCount[normalizedColor] ?: 0) + 1
        }

        Log.d("SimpleMainActivity", "各颜色区域分布:")
        data.colorPalette.forEach { palette ->
            val normalizedColor = palette.colorHex.lowercase()
            val count = colorRegionCount[normalizedColor] ?: 0
//            Log.d("SimpleMainActivity", "  ${palette.name} (${palette.colorHex}): $count 个区域")
        }

        // 检查是否有孤立的区域（没有对应调色板颜色）
        val paletteColors = data.colorPalette.map { it.colorHex.lowercase() }.toSet()
        val orphanRegions = data.regions.filter { region ->
            !paletteColors.contains(region.colorHex.lowercase())
        }

        if (orphanRegions.isNotEmpty()) {
            Log.w("SimpleMainActivity", "发现 ${orphanRegions.size} 个孤立区域（没有对应调色板颜色）:")
            orphanRegions.forEach { region ->
                Log.w("SimpleMainActivity", "  区域 ${region.id}: ${region.colorHex}")
            }
        }

        // 检查是否有调色板颜色没有对应区域
        val regionColors = data.regions.map { it.colorHex.lowercase() }.toSet()
        val unusedColors = data.colorPalette.filter { palette ->
            !regionColors.contains(palette.colorHex.lowercase())
        }

        if (unusedColors.isNotEmpty()) {
            Log.w("SimpleMainActivity", "发现 ${unusedColors.size} 个未使用的调色板颜色:")
            unusedColors.forEach { palette ->
                Log.w("SimpleMainActivity", "  ${palette.name}: ${palette.colorHex}")
            }
        }

        Log.d("SimpleMainActivity", "=== 检查完成 ===")
    }

    /**
     * 切换自动演示模式
     */
    private fun toggleAutoDemo() {
        Log.d("SimpleMainActivity", "toggleAutoDemo called, isAutoDemo=$isAutoDemo")
        if (isAutoDemo) {
            Log.d("SimpleMainActivity", "停止演示")
            stopAutoDemo()
        } else {
            Log.d("SimpleMainActivity", "开始演示")
            startAutoDemo()
        }
    }

    /**
     * 开始自动演示
     */
    private fun startAutoDemo() {
        Log.d("SimpleMainActivity", "startAutoDemo called")
        val coloringData = currentColoringData
        if (coloringData == null) {
            Log.w("SimpleMainActivity", "currentColoringData is null")
            return
        }

        Log.d("SimpleMainActivity", "项目数据已加载，区域数: ${coloringData.regions.size}")

        isAutoDemo = true
        currentRegionIndex = 0
//        binding.btnAutoDemo.text = "停止"

        // 重置填色状态
        filledRegions.clear()
        binding.coloringView.resetColoring()
        updateProgress()

        // 设置为适合屏幕的大小，不进行自动跟踪
        binding.coloringView.zoomToFit()

        val message = "开始自动演示 - 每0.2秒填充一个区域 (共${coloringData.regions.size}个区域)"
//        Toast.makeText(this, message, Toast.LENGTH_LONG).show()
        Log.d("SimpleMainActivity", "开始自动演示，总共 ${coloringData.regions.size} 个区域")

        // 开始自动填色
        scheduleNextAutoFill()
    }

    /**
     * 停止自动演示
     */
    private fun stopAutoDemo() {
        isAutoDemo = false
//        binding.btnAutoDemo.text = "演示"

        // 取消定时任务
        autoRunnable?.let { autoHandler.removeCallbacks(it) }
        autoRunnable = null

        Log.d("SimpleMainActivity", "自动演示已停止")
    }

    /**
     * 安排下一次自动填色
     */
    private fun scheduleNextAutoFill() {
        if (!isAutoDemo) return

        val coloringData = currentColoringData ?: return

        // 检查是否还有区域需要填色
        if (currentRegionIndex >= coloringData.regions.size) {
            // 所有区域都已填色，演示完成
            completeAutoDemo()
            return
        }

        autoRunnable = Runnable {
            performAutoFill()
        }

        // 0.2秒后执行下一次填色
        autoHandler.postDelayed(autoRunnable!!, 10)
    }

    /**
     * 执行自动填色
     */
    private fun performAutoFill() {
        if (!isAutoDemo) {
            Log.d("SimpleMainActivity", "演示已停止，跳过填色")
            return
        }

        val coloringData = currentColoringData ?: return

        if (currentRegionIndex >= coloringData.regions.size) {
            Log.d("SimpleMainActivity", "所有区域已完成，结束演示")
            completeAutoDemo()
            return
        }

        val region = coloringData.regions[currentRegionIndex]

        Log.d("SimpleMainActivity", "自动填色区域 ${region.id} (${currentRegionIndex + 1}/${coloringData.regions.size})")

        // 找到对应的调色板颜色
        val paletteColor = coloringData.colorPalette.find {
            it.colorHex.equals(region.colorHex, ignoreCase = true)
        }

        if (paletteColor != null) {
            Log.d("SimpleMainActivity", "找到匹配颜色: ${paletteColor.name} (${paletteColor.colorHex})")

            // 选择对应的颜色
            selectColor(paletteColor)

            // 演示时不进行自动跟踪，保持适合屏幕的大小

            // 模拟填色
            filledRegions.add(region.id)
            binding.coloringView.addFilledRegion(region.id)

            // 更新进度
            updateProgress()

            // 每10个区域显示一次进度（避免Toast过多）
            if (currentRegionIndex % 10 == 0 || currentRegionIndex == coloringData.regions.size - 1) {
                val progressText = "${currentRegionIndex + 1}/${coloringData.regions.size}"
//                Toast.makeText(this, "填色进度: $progressText", Toast.LENGTH_SHORT).show()
            }
        } else {
            Log.w("SimpleMainActivity", "未找到区域 ${region.id} 的匹配颜色: ${region.colorHex}")
        }

        currentRegionIndex++

        // 安排下一次填色
        scheduleNextAutoFill()
    }

    /**
     * 完成自动演示
     */
    private fun completeAutoDemo() {
        isAutoDemo = false
//        binding.btnAutoDemo.text = "演示"

        Log.d("SimpleMainActivity", "自动演示完成")

        // 显示完成对话框
        showCompletionDialog()
    }

    override fun onBackPressed() {
        // 优化退出策略：快速捕获预览图 + 后台保存
        if (currentColoringData != null && filledRegions.isNotEmpty()) {
            Log.d(TAG, "退出时启动后台保存任务")

            // 1. 立即捕获预览图（在UI线程，但很快）
            val previewBitmap = try {
                binding.coloringView.captureArtwork(false)
            } catch (e: Exception) {
                Log.w(TAG, "快速捕获预览图失败", e)
                null
            }

            // 2. 启动后台保存任务，传入预览图
            startBackgroundSaveTask(previewBitmap)

            // 3. 立即退出，不等待保存完成
            finishActivity()
        } else {
            // 没有需要保存的内容，直接退出
            super.onBackPressed()
        }
    }



    /**
     * 启动后台保存任务，不阻塞UI退出
     */
    private fun startBackgroundSaveTask(capturedPreviewBitmap: Bitmap? = null) {
        val projectName = getStandardizedProjectName()
        val coloringData = currentColoringData
        val currentFilledRegions = filledRegions.toSet() // 创建副本避免并发问题

        if (coloringData == null) {
            Log.w(TAG, "coloringData为空，跳过后台保存")
            return
        }

        // 设置标记，避免onPause重复保存
        backgroundSaveStarted = true
        Log.d(TAG, "启动后台保存任务: $projectName, 填色区域: ${currentFilledRegions.size}, 预览图: ${capturedPreviewBitmap != null}")

        // 使用GlobalScope确保即使Activity销毁也能完成保存
        GlobalScope.launch(Dispatchers.IO) {
            try {
                val startTime = System.currentTimeMillis()

                // 1. 快速保存进度数据
                val projectSaveManager = com.example.coloringproject.utils.ProjectSaveManager(this@SimpleMainActivity)
                val saveResult = projectSaveManager.saveProgressFast(
                    projectName = projectName,
                    coloringData = coloringData,
                    filledRegions = currentFilledRegions
                )

                val saveTime = System.currentTimeMillis() - startTime
                Log.d(TAG, "后台进度保存完成: $projectName, 耗时: ${saveTime}ms")

                // 2. 处理预览图片
                val previewStartTime = System.currentTimeMillis()
                var previewBitmap: Bitmap? = capturedPreviewBitmap

                // 如果没有传入预览图且Activity还存在，尝试从ColoringView捕获
                if (previewBitmap == null && !isDestroyed && !isFinishing) {
                    try {
                        previewBitmap = withContext(Dispatchers.Main) {
                            if (!isDestroyed && !isFinishing) {
                                Log.d(TAG, "从ColoringView捕获预览图: $projectName")
                                binding.coloringView.captureArtwork(false)
                            } else {
                                null
                            }
                        }
                    } catch (e: Exception) {
                        Log.w(TAG, "从ColoringView捕获预览图失败: $projectName", e)
                        previewBitmap = null
                    }
                } else if (previewBitmap != null) {
                    Log.d(TAG, "使用退出时捕获的预览图: $projectName")
                }

                // 如果无法从ColoringView捕获，检查是否有之前保存的预览图
                if (previewBitmap == null) {
                    Log.d(TAG, "Activity已销毁或捕获失败，检查已保存的预览图: $projectName")
                    val existingPreviewPath = projectSaveManager.getPreviewImagePath(projectName)
                    if (existingPreviewPath != null && File(existingPreviewPath).exists()) {
                        Log.d(TAG, "找到已存在的预览图，将触发刷新: $projectName")
                        // 即使没有新的预览图，也要通知刷新以更新进度显示
                    } else {
                        Log.d(TAG, "没有找到已保存的预览图: $projectName")
                    }
                }

                // 保存预览图（如果有的话）
                if (previewBitmap != null) {
                    try {
                        val thumbnailBitmap = createThumbnailBitmap(previewBitmap)
                        projectSaveManager.savePreviewImage(projectName, thumbnailBitmap)

                        if (thumbnailBitmap != previewBitmap) {
                            previewBitmap.recycle()
                        }

                        val previewTime = System.currentTimeMillis() - previewStartTime
                        Log.d(TAG, "后台预览图生成完成: $projectName, 耗时: ${previewTime}ms")
                    } catch (e: Exception) {
                        Log.e(TAG, "保存预览图失败: $projectName", e)
                    }
                }

                // 无论是否有预览图，都要发送通知
                withContext(Dispatchers.Main) {
                    val progressPercentage = (currentFilledRegions.size * 100) / coloringData.regions.size
                    Log.d(TAG, "后台任务完成，发送刷新通知: $projectName, 进度: $progressPercentage%")

                    // 发送进度更新通知
                    LibraryEventManager.notifyProjectProgressUpdated(
                        projectName,
                        currentFilledRegions.isNotEmpty(),
                        progressPercentage
                    )

                    // 发送预览图更新通知（即使没有新预览图，也要触发刷新）
                    val previewPath = projectSaveManager.getPreviewImagePath(projectName)
                    LibraryEventManager.notifyProjectPreviewUpdated(projectName, previewPath)

                    Log.d(TAG, "后台通知发送完成: $projectName")
                }

                val totalTime = System.currentTimeMillis() - startTime
                Log.d(TAG, "后台保存任务完全完成: $projectName, 总耗时: ${totalTime}ms")

            } catch (e: Exception) {
                Log.e(TAG, "后台保存任务失败: $projectName", e)
            }
        }
    }

    /**
     * 安全退出Activity
     */
    private fun finishActivity() {
        super.onBackPressed()
    }

    /**
     * 设置共享元素过渡动画
     */
    private fun setupSharedElementTransition() {
        // 获取项目ID
        val projectId = intent.getStringExtra("project_id") ?: intent.getStringExtra("project_name") ?: "default"

        // 设置ColoringView的transitionName，与Library中的ImageView对应
        binding.coloringView.transitionName = "project_image_$projectId"

        // 启用共享元素过渡
        window.sharedElementEnterTransition = android.transition.ChangeBounds().apply {
            duration = 300
        }
        window.sharedElementExitTransition = android.transition.ChangeBounds().apply {
            duration = 300
        }

        // 设置进入和退出过渡
        window.enterTransition = android.transition.Fade().apply {
            duration = 200
        }
        window.exitTransition = android.transition.Fade().apply {
            duration = 200
        }

        Log.d(TAG, "设置共享元素过渡: project_image_$projectId")
    }

    // 标记是否已经启动了后台保存任务，避免重复保存
    private var backgroundSaveStarted = false

    override fun onPause() {
        super.onPause()

        // 如果还没有启动后台保存任务，则在onPause时保存
        if (currentColoringData != null && filledRegions.isNotEmpty() && !backgroundSaveStarted) {
            Log.d(TAG, "onPause: 开始保存进度")
            saveCurrentProgress()
            // 异步生成预览图
            generatePreviewImageAsync()
        } else {
            Log.d(TAG, "onPause: 无需保存进度或已启动后台保存")
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        try {
            // 清理定时任务
            autoRunnable?.let { autoHandler.removeCallbacks(it) }
            
            // 停止内存监控
//            memoryManager.stopMemoryMonitoring()
            
            // 清理预览图片生成任务
            previewGenerationRunnable?.let { previewGenerationHandler.removeCallbacks(it) }
            
            Log.d(TAG, "SimpleMainActivity destroyed and resources cleaned up")
        } catch (e: Exception) {
            Log.e(TAG, "Error during onDestroy cleanup", e)
        }
    }


    /**
     * 保存画作到相册
     */
    private fun saveArtworkToGallery() {
        // 检查权限
        if (!PermissionHelper.hasStoragePermission(this)) {
            if (PermissionHelper.shouldShowPermissionRationale(this)) {
                // 显示权限说明
                AlertDialog.Builder(this)
                    .setTitle("需要存储权限")
                    .setMessage(PermissionHelper.getPermissionRationaleMessage())
                    .setPositiveButton("授权") { _, _ ->
                        PermissionHelper.requestStoragePermission(this)
                    }
                    .setNegativeButton("取消", null)
                    .show()
            } else {
                PermissionHelper.requestStoragePermission(this)
            }
            return
        }

        // 显示保存选项对话框
        showSaveOptionsDialog()
    }

    /**
     * 显示保存选项对话框
     */
    private fun showSaveOptionsDialog() {
        val options = arrayOf(
            "保存填色画作",
            "保存对比图（原图+填色图）",
            "保存完整作品集"
        )

        AlertDialog.Builder(this)
            .setTitle("选择保存内容")
            .setItems(options) { _, which ->
                when (which) {
                    0 -> saveFilledArtwork()
                    1 -> saveComparisonArtwork()
                    2 -> saveArtworkCollection()
                }
            }
            .show()
    }

    /**
     * 保存填色画作
     */
    private fun saveFilledArtwork() {
        val artwork = binding.coloringView.captureArtwork(false)
        if (artwork != null) {
            lifecycleScope.launch {
                val success = ImageSaver.saveImageToGallery(
                    this@SimpleMainActivity,
                    artwork,
                    "coloring_artwork_${System.currentTimeMillis()}.jpg"
                )
                ImageSaver.showSaveResult(this@SimpleMainActivity, success)
            }
        }
    }

    /**
     * 保存对比画作
     */
    private fun saveComparisonArtwork() {
        val artwork = binding.coloringView.captureArtwork(true)
        if (artwork != null) {
            lifecycleScope.launch {
                val success = ImageSaver.saveImageToGallery(
                    this@SimpleMainActivity,
                    artwork,
                    "coloring_comparison_${System.currentTimeMillis()}.jpg"
                )
                ImageSaver.showSaveResult(this@SimpleMainActivity, success)
            }
        } else {
        }
    }

    /**
     * 保存完整作品集
     */
    private fun saveArtworkCollection() {
        val filledArtwork = binding.coloringView.captureArtwork(false)
        if (filledArtwork != null) {
            lifecycleScope.launch {
                val currentProject = viewModel.currentProject.value
                val projectName = currentProject?.name ?: "artwork"

                val result = ImageSaver.saveArtworkCollection(
                    this@SimpleMainActivity,
                    filledArtwork,
                    null, // 可以传入原图bitmap
                    projectName
                )
            }
        } else {
        }
    }

    /**
     * 处理权限请求结果
     */
    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<out String>,
        grantResults: IntArray
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)

        if (PermissionHelper.handlePermissionResult(requestCode, permissions, grantResults)) {
            // 权限获取成功，继续保存
            showSaveOptionsDialog()
        }
    }

    /**
     * 使用涂色提醒
     */
    private fun useHint() {
        if (hintCount > 0) {
            hintCount--
            updateHintCountDisplay()

            // 执行提醒功能 - 高亮下一个需要填色的区域
            binding.coloringView.showNextColorHint()


        }
    }

    /**
     * 更新提醒次数显示
     */
    private fun updateHintCountDisplay() {
        binding.tvHintCount.text = hintCount.toString()
        binding.tvHintCount.visibility = if (hintCount > 0) View.VISIBLE else View.GONE
    }

    /**
     * 🚀 新增：简化的调色板处理（不计算进度）
     */
    private fun processColorPaletteSimple(originalPalette: List<ColorPalette>): List<ColorPalette> {
        return originalPalette.map { palette ->
            ColorPalette(
                id = palette.id,
                colorHex = palette.colorHex,
                colorRgb = palette.colorRgb,
                name = palette.name,
                usageCount = palette.usageCount,
                filledCount = 0, // 不预计算
                totalCount = 0   // 不预计算
            )
        }
    }

    /**
     * 转换ColorPalette到ColorInfo
     */
    private fun convertToColorInfo(colorPalette: List<ColorPalette>): List<ColorInfo> {
        return colorPalette.mapIndexed { index, palette ->
            ColorInfo(
                id = index,
                name = palette.name,
                hexColor = palette.colorHex
            )
        }
    }

    /**
     * 根据项目ID和来源加载项目
     * 这是新的优化加载方式，支持轻量级项目信息
     */
    private fun loadProjectById(projectId: String, projectSource: String?) {
        Log.d(TAG, "加载项目: ID=$projectId, Source=$projectSource")
        lifecycleScope.launch {
            try {
                // 显示加载状态
                runOnUiThread {
                    showLoadingIndicator()
                }

                // 根据项目来源选择加载方式
                Log.i(TAG, "项目来源判断: $projectSource")
                when (projectSource) {
                    "BUILT_IN" -> {
                        Log.d(TAG, "加载内置项目: $projectId")
                        // 从Assets加载内置项目
                        loadBuiltInProject(projectId)
                    }
                    "REMOTE_DOWNLOADED" -> {
                        Log.d(TAG, "加载已下载项目: $projectId")
                        // 从下载目录加载
                        loadDownloadedProject(projectId)
                    }
                    "STREAMING" -> {
                        Log.d(TAG, "加载流式项目: $projectId")
                        // 流式下载项目
                        downloadAndLoadRemoteProject(projectId)
                    }
                    else -> {
                        Log.d(TAG, "使用混合管理器加载项目: $projectId")
                        // 使用混合资源管理器
                        lifecycleScope.launch {
                            loadProjectWithHybridManager(projectId)
                        }
                    }
                }

            } catch (e: Exception) {
                Log.e(TAG, "加载项目时出错: $projectId", e)
                runOnUiThread {
                    hideLoadingIndicator()
                    showProjectLoadError("项目加载异常", "加载项目 $projectId 时发生异常: ${e.message}\n\n将自动加载默认项目。")
                    // 回退到加载默认项目
                    loadFirstValidatedProject()
                }
            }
        }
    }

    /**
     * 加载内置项目
     */
    private suspend fun loadBuiltInProject(projectId: String) {
        try {
            // 在Debug模式下进行详细诊断
            if (BuildConfig.DEBUG) {
                Log.d(TAG, "开始诊断项目: $projectId")

                val validationResult = com.example.coloringproject.test.SimpleProjectDebugger.validateProject(this, projectId)
                Log.d(TAG, "项目验证结果: $validationResult")

                if (!validationResult.isValid) {
                    runOnUiThread {
                        hideLoadingIndicator()
                        showProjectLoadError(
                            "项目验证失败",
                            "项目 $projectId 验证失败:\n${validationResult.getErrorSummary()}"
                        )
                        return@runOnUiThread
                    }
                }
            }
            val result = enhancedAssetManager.loadColoringData("$projectId.json")
            result.fold(
                onSuccess = { coloringData ->
                    val outlineResult = enhancedAssetManager.loadOutlineBitmap("$projectId.png")
                    outlineResult.fold(
                        onSuccess = { outlineBitmap ->
                            runOnUiThread {
                                // 隐藏加载指示器
                                hideLoadingIndicator()

                                // 使用统一的项目设置方法
                                setupProject(coloringData, outlineBitmap)
                                loadSavedProgress(projectId) // 加载保存的进度

                            }
                        },
                        onFailure = { error ->
                            Log.e(TAG, "加载轮廓图片失败: $projectId", error)
                            runOnUiThread {
                                hideLoadingIndicator()
                                showProjectLoadError("加载图片失败", "无法加载项目 $projectId 的轮廓图片: ${error.message}")
                                loadFirstValidatedProject()
                            }
                        }
                    )
                },
                onFailure = { error ->
                    Log.e(TAG, "加载项目数据失败: $projectId", error)
                    runOnUiThread {
                        hideLoadingIndicator()
                        showProjectLoadError("加载数据失败", "无法加载项目 $projectId 的数据文件: ${error.message}")
                        loadFirstValidatedProject()
                    }
                }
            )
        } catch (e: Exception) {
            Log.e(TAG, "加载内置项目时出错: $projectId", e)
            runOnUiThread {
                hideLoadingIndicator()
                showProjectLoadError("加载项目失败", "加载项目 $projectId 时发生异常: ${e.message}")
                loadFirstValidatedProject()
            }
        }
    }

    /**
     * 加载已下载项目
     */
    private suspend fun loadDownloadedProject(projectId: String) {
        try {
            // TODO: 实现从下载目录加载项目的逻辑
            Log.w(TAG, "下载项目加载功能待实现: $projectId")
            runOnUiThread {
                loadFirstValidatedProject()
            }
        } catch (e: Exception) {
            Log.e(TAG, "加载下载项目时出错: $projectId", e)
            runOnUiThread {
                loadFirstValidatedProject()
            }
        }
    }

    /**
     * 使用HybridResourceManager加载项目
     */
    private suspend fun loadProjectWithHybridManager(projectId: String) {
        try {
            val result = hybridResourceManager.loadProjectResource(projectId)

            when (result) {
                is HybridResourceManager.ResourceLoadResult.Success -> {
                    Log.d(TAG, "项目加载成功: $projectId")
                    runOnUiThread {
                        hideLoadingIndicator()
                        // 使用统一的项目设置方法
                        setupProject(result.coloringData, result.outlineBitmap)
                        loadSavedProgress(projectId) // 加载保存的进度
                    }
                }

                is HybridResourceManager.ResourceLoadResult.RequiresDownload -> {
                    Log.w(TAG, "项目需要下载: $projectId")
                    runOnUiThread {
                        hideLoadingIndicator()
                        showDownloadDialog(result.project, result.downloadUrl)
                    }
                }

                is HybridResourceManager.ResourceLoadResult.Error -> {
                    Log.e(TAG, "项目加载失败: ${result.message}")
                    runOnUiThread {
                        hideLoadingIndicator()
                        showProjectLoadError("加载失败", result.message)
                        loadFirstValidatedProject()
                    }
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "使用HybridResourceManager加载项目时出错: $projectId", e)
            runOnUiThread {
                hideLoadingIndicator()
                showProjectLoadError("加载错误", "加载项目时发生错误: ${e.message}")
                loadFirstValidatedProject()
            }
        }
    }

    /**
     * 自动下载并加载远程项目（支持缓存）
     */
    private fun downloadAndLoadRemoteProject(projectId: String) {
        lifecycleScope.launch {
            try {
                Log.d(TAG, "开始处理远程项目: $projectId")

                runOnUiThread {
                    showLoadingIndicator()
                }

                // 首先检查是否已经下载过
                val downloadDir = File(filesDir, "downloaded_projects/$projectId")
                val jsonFile = File(downloadDir, "$projectId.json")
                val pngFile = File(downloadDir, "$projectId.png")

                if (jsonFile.exists() && pngFile.exists()) {
                    // 已经下载过，直接加载缓存文件
                    Log.d(TAG, "发现缓存文件，直接加载: $projectId")
                    loadCachedProjectFiles(projectId, jsonFile, pngFile)
                } else {
                    // 需要下载
                    Log.d(TAG, "缓存文件不存在，开始下载: $projectId")

                    // 确保下载目录存在
                    if (!downloadDir.exists()) {
                        downloadDir.mkdirs()
                    }

                    // 使用ResourceDownloadManager下载项目
                    val downloadManager = ResourceDownloadManager(this@SimpleMainActivity)
                    val downloadResult = downloadManager.downloadProject(projectId)

                    downloadResult.fold(
                        onSuccess = { downloadedProject ->
                            Log.d(TAG, "项目下载成功: ${downloadedProject.projectId}")

                            // 加载下载的项目
                            loadDownloadedProjectFiles(downloadedProject)
                        },
                        onFailure = { error ->
                            Log.e(TAG, "项目下载失败: $projectId", error)
                            runOnUiThread {
                                hideLoadingIndicator()
                                Toast.makeText(this@SimpleMainActivity, "下载失败: ${error.message}", Toast.LENGTH_LONG).show()
                                loadFirstValidatedProject()
                            }
                        }
                    )
                }

            } catch (e: Exception) {
                Log.e(TAG, "处理远程项目时出错: $projectId", e)
                runOnUiThread {
                    hideLoadingIndicator()
                    loadFirstValidatedProject()
                }
            }
        }
    }

    /**
     * 加载缓存的项目文件
     */
    private suspend fun loadCachedProjectFiles(projectId: String, jsonFile: File, pngFile: File) {
        try {
            Log.d(TAG, "加载缓存项目文件: $projectId")

            // 从缓存文件加载数据
            val coloringDataResult = enhancedAssetManager.loadColoringData(jsonFile.absolutePath)
            val outlineBitmapResult = enhancedAssetManager.loadOutlineBitmap(pngFile.absolutePath)

            if (coloringDataResult.isSuccess && outlineBitmapResult.isSuccess) {
                val coloringData = coloringDataResult.getOrNull()!!
                val outlineBitmap = outlineBitmapResult.getOrNull()!!

                runOnUiThread {
                    // 隐藏加载指示器
                    hideLoadingIndicator()

                    // 使用统一的项目设置方法
                    setupProject(coloringData, outlineBitmap)
                    loadSavedProgress(projectId) // 加载保存的进度

                }
            } else {
                Log.e(TAG, "加载缓存文件失败，尝试重新下载")
                // 删除损坏的缓存文件
                jsonFile.delete()
                pngFile.delete()
                // 重新下载
                downloadAndLoadRemoteProject(projectId)
            }

        } catch (e: Exception) {
            Log.e(TAG, "加载缓存项目文件时出错", e)
            runOnUiThread {
                hideLoadingIndicator()
            }
            // 删除损坏的缓存文件并重新下载
            jsonFile.delete()
            pngFile.delete()
            downloadAndLoadRemoteProject(projectId)
        }
    }

    /**
     * 加载已下载的项目文件
     */
    private suspend fun loadDownloadedProjectFiles(downloadedProject: ResourceDownloadManager.DownloadedProject) {
        try {
            Log.d(TAG, "加载已下载的项目文件: ${downloadedProject.projectId}")

            // 从下载的文件加载数据
            val coloringDataResult = enhancedAssetManager.loadColoringData(downloadedProject.jsonFile.absolutePath)
            val outlineBitmapResult = enhancedAssetManager.loadOutlineBitmap(downloadedProject.outlineFile.absolutePath)

            if (coloringDataResult.isSuccess && outlineBitmapResult.isSuccess) {
                val coloringData = coloringDataResult.getOrNull()!!
                val outlineBitmap = outlineBitmapResult.getOrNull()!!

                runOnUiThread {
                    // 隐藏加载指示器
                    hideLoadingIndicator()

                    // 使用统一的项目设置方法
                    setupProject(coloringData, outlineBitmap)
                    loadSavedProgress(downloadedProject.projectId) // 加载保存的进度

                }
            } else {
                runOnUiThread {
                    hideLoadingIndicator()
                    loadFirstValidatedProject()
                }
            }

        } catch (e: Exception) {
            Log.e(TAG, "加载已下载项目文件时出错", e)
            runOnUiThread {
                hideLoadingIndicator()
                loadFirstValidatedProject()
            }
        }
    }

    /**
     * 显示需要下载的对话框（保留备用）
     */
    private fun showDownloadRequiredDialog(projectId: String) {
        AlertDialog.Builder(this)
            .setTitle("需要下载")
            .setMessage("项目 '$projectId' 需要从服务器下载。\n\n是否现在下载？")
            .setPositiveButton("下载") { _, _ ->
                downloadAndLoadRemoteProject(projectId)
            }
            .setNegativeButton("取消") { _, _ ->
                loadFirstValidatedProject()
            }
            .setCancelable(false)
            .show()
    }

    /**
     * 显示下载对话框
     */
    private fun showDownloadDialog(project: HybridResourceManager.HybridProject, downloadUrl: String) {
        AlertDialog.Builder(this)
            .setTitle("需要下载")
            .setMessage("项目 '${project.displayName}' 需要从服务器下载。\n\n是否现在下载？")
            .setPositiveButton("下载") { _, _ ->
                // TODO: 实现下载功能
                Toast.makeText(this, "下载功能开发中...", Toast.LENGTH_SHORT).show()
            }
            .setNegativeButton("取消") { _, _ ->
                // 回退到加载默认项目
                loadFirstValidatedProject()
            }
            .setCancelable(false)
            .show()
    }

    /**
     * 更新项目信息显示
     */
    private fun updateProjectInfo(metadata: Metadata) {
        // 可以在这里更新项目相关的UI信息
        Log.d(TAG, "项目信息: 版本=${metadata.version}, 区域数=${metadata.totalRegions}, 颜色数=${metadata.totalColors}")
    }

    /**
     * 显示加载指示器
     */
    private fun showLoadingIndicator() {
        try {
            // 查找并显示可能的加载指示器
            val loadingIndicator = findViewById<View>(R.id.loadingIndicator)
            loadingIndicator?.visibility = View.VISIBLE

            val progressBar = findViewById<View>(R.id.progressBar)
            progressBar?.visibility = View.VISIBLE

            val loadingText = findViewById<TextView>(R.id.tvStatus)
            loadingText?.apply {
                visibility = View.VISIBLE
                text = "正在加载项目..."
            }

            // 隐藏填色UI
            binding.recyclerViewColors.visibility = View.GONE

            Log.d(TAG, "加载指示器已显示")
        } catch (e: Exception) {
            Log.w(TAG, "显示加载指示器时出错", e)
        }
    }

    /**
     * 隐藏加载指示器
     */
    private fun hideLoadingIndicator() {
        try {
            // 查找并隐藏可能的加载指示器
            val loadingIndicator = findViewById<View>(R.id.loadingIndicator)
            loadingIndicator?.visibility = View.GONE

//            val progressBar = findViewById<View>(R.id.progressBar)
//            progressBar?.visibility = View.GONE

            val loadingText = findViewById<View>(R.id.tvStatus)
            loadingText?.visibility = View.GONE

            Log.d(TAG, "加载指示器已隐藏")
        } catch (e: Exception) {
            Log.w(TAG, "隐藏加载指示器时出错", e)
        }
    }

    /**
     * 显示填色UI控件
     */
    private fun showColoringUI() {
        try {
            // 显示颜色面板
            binding.recyclerViewColors.visibility = View.VISIBLE

            // 显示底部控件
            val bottomPanel = findViewById<View>(R.id.bottomPanel)
            bottomPanel?.visibility = View.VISIBLE

            Log.d(TAG, "填色UI已显示")
        } catch (e: Exception) {
            Log.w(TAG, "显示填色UI时出错", e)
        }
    }

    /**
     * 从填色数据更新颜色调色板
     */
    private fun updateColorPaletteFromData(colorPalette: List<ColorPalette>) {
        try {
            // 转换为ColorInfo列表
            val colorInfoList = colorPalette.map { palette ->
                ColorInfo(
                    id = palette.id,
                    name = palette.name,
                    hexColor = palette.colorHex
                )
            }

            // 更新沉浸式适配器（不使用colorPaletteAdapter）
            immersiveColorAdapter.updateColors(colorInfoList)

            Log.d(TAG, "颜色调色板已更新: ${colorInfoList.size} 种颜色")
        } catch (e: Exception) {
            Log.e(TAG, "更新颜色调色板失败", e)
        }
    }

    /**
     * 使用预加载数据初始化涂色界面
     */
    private fun initializeWithPreloadedData(preloadedData: ProjectPreloadManager.PreloadedProjectData) {
        try {
            Log.d(TAG, "开始使用预加载数据初始化: ${preloadedData.projectId}")

            // 设置涂色数据
            currentColoringData = preloadedData.coloringData
            binding.coloringView.setColoringData(preloadedData.coloringData, preloadedData.outlineBitmap)

            // 设置预处理的调色板
            updateColorPaletteWithPreprocessed(preloadedData.processedPalette)

            // 立即恢复进度（同步执行，确保在显示界面前完成）
            restoreProgressSync(preloadedData.projectId)

            // 重要：完成完整的UI初始化
            completeUIInitialization()

            // 立即显示涂色界面，跳过过渡动画和加载指示器
            showColoringViewImmediately()

            Log.d(TAG, "预加载数据初始化完成: ${preloadedData.projectId}")

        } catch (e: Exception) {
            Log.e(TAG, "预加载数据初始化失败: ${preloadedData.projectId}", e)
            // 回退到常规加载
            loadProjectById(preloadedData.projectId, null)
        }
    }

    /**
     * 完成完整的UI初始化
     */
    private fun completeUIInitialization() {
        try {
            Log.d(TAG, "开始完成UI初始化")

            // 1. 设置长按监听器
            setupLongPressListener()

            // 2. 更新所有UI状态
            updateAllUIStates()

            // 3. 设置默认选中颜色（在适配器更新后）
            // 使用post确保在下一个UI循环中执行，此时适配器已经完全初始化
            binding.recyclerViewColors.post {
                selectDefaultColor()
            }

            Log.d(TAG, "UI初始化完成")
        } catch (e: Exception) {
            Log.e(TAG, "UI初始化失败", e)
        }
    }

    /**
     * 选择默认颜色
     */
    private fun selectDefaultColor() {
        try {
            if (currentColoringData != null && currentColoringData!!.colorPalette.isNotEmpty()) {
                // 选择第一个颜色作为默认颜色
                val firstColor = currentColoringData!!.colorPalette[0]

                // 使用现有的selectColor方法
                selectColor(firstColor)

                Log.d(TAG, "默认选中颜色: ${firstColor.id} (${firstColor.colorHex})")
            } else {
                Log.w(TAG, "无可用颜色，无法设置默认选中")
            }
        } catch (e: Exception) {
            Log.e(TAG, "设置默认颜色失败", e)
        }
    }

    /**
     * 设置长按监听器
     */
    private fun setupLongPressListener() {
        try {
            // ColoringView已经有长按自动选择颜色的功能
            // 我们只需要确保onColorAutoSelected回调已设置
            binding.coloringView.onColorAutoSelected = { colorHex ->
                runOnUiThread {
                    autoSelectColorByHex(colorHex)
                }
            }
            Log.d(TAG, "长按监听器设置完成")
        } catch (e: Exception) {
            Log.e(TAG, "设置长按监听器失败", e)
        }
    }

    /**
     * 显示颜色选中提示
     */
    private fun showColorSelectedHint(color: ColorPalette) {
        try {
            // 可以显示一个简短的Toast或者其他提示
            android.widget.Toast.makeText(
                this,
                "已选中颜色 ${color.id}",
                android.widget.Toast.LENGTH_SHORT
            ).show()
        } catch (e: Exception) {
            Log.e(TAG, "显示颜色提示失败", e)
        }
    }

    /**
     * 更新所有UI状态
     */
    private fun updateAllUIStates() {
        try {
            // 更新进度显示
            updateProgress()

            // 更新所有颜色的进度
            updateAllColorProgress()

            Log.d(TAG, "所有UI状态更新完成")
        } catch (e: Exception) {
            Log.e(TAG, "更新UI状态失败", e)
        }
    }

    /**
     * 立即显示涂色界面
     */
    private fun showColoringViewImmediately() {
        // 隐藏所有可能的加载UI元素
        hideAllLoadingElements()

        // 显示涂色界面
        binding.coloringView.visibility = View.VISIBLE
        binding.coloringView.alpha = 1f

        Log.d(TAG, "立即显示涂色界面")
    }

    /**
     * 隐藏所有加载相关的UI元素
     */
    private fun hideAllLoadingElements() {
        try {
            // 隐藏可能存在的加载UI元素
            if (::binding.isInitialized) {
                // 隐藏圆形进度指示器
                binding.loadingIndicator.visibility = View.GONE

                // 隐藏进度条
                binding.progressBar.visibility = View.GONE
            }

            Log.d(TAG, "所有加载UI元素已隐藏")
        } catch (e: Exception) {
            Log.e(TAG, "隐藏加载UI元素时出错", e)
        }
    }

    /**
     * 使用预处理的调色板更新UI
     */
    private fun updateColorPaletteWithPreprocessed(processedPalette: List<ColorPalette>) {
        try {
            Log.d(TAG, "开始使用预处理调色板更新UI，颜色数量: ${processedPalette.size}")

            // 缓存预处理的调色板
            cachedProcessedPalette = processedPalette

            // 转换为ColorInfo列表
            val colorInfoList = convertToColorInfo(processedPalette)

            // 更新适配器颜色列表
            immersiveColorAdapter.updateColors(colorInfoList, preserveProgress = false)

            // 更新所有颜色的进度
            processedPalette.forEachIndexed { index, palette ->
                val progress = if (palette.totalCount > 0) {
                    (palette.filledCount * 100) / palette.totalCount
                } else {
                    0
                }
                immersiveColorAdapter.updateColorProgress(index, progress)
                Log.d(TAG, "设置颜色进度: ${palette.name} (index=$index) = $progress%")
            }

            // 强制刷新适配器
            immersiveColorAdapter.forceRefreshAll()

            Log.d(TAG, "预处理调色板更新完成，颜色数量: ${processedPalette.size}")
        } catch (e: Exception) {
            Log.e(TAG, "更新预处理调色板失败", e)
        }
    }

    /**
     * 同步恢复项目进度 - 用于预加载快速启动
     */
    private fun restoreProgressSync(projectName: String) {
        try {
            Log.d(TAG, "=== 开始同步恢复进度 ===")
            Log.d(TAG, "项目名称: $projectName")

            val projectSaveManager = com.example.coloringproject.utils.ProjectSaveManager(this)

            // 使用ProjectSaveManager的正确方法加载进度数据
            val result = projectSaveManager.loadFullProgress(projectName)

            when (result) {
                is com.example.coloringproject.utils.LoadResult.Success -> {
                    val fullProgressData = result.data
                    Log.d(TAG, "找到完整进度数据: ${fullProgressData.filledRegions.size}个填色区域")

                    if (fullProgressData.filledRegions.isNotEmpty()) {
                        // 恢复填色区域
                        filledRegions.clear()
                        filledRegions.addAll(fullProgressData.filledRegions)

                        // 更新ColoringView
                        binding.coloringView.setFilledRegions(filledRegions)

                        // 更新进度显示
                        updateProgress()
                        updateAllColorProgress()

                        Log.d(TAG, "同步恢复进度成功: $projectName, 已填色区域: ${filledRegions.size}")
                    } else {
                        Log.d(TAG, "进度数据存在但无填色区域: $projectName")
                    }
                }
                is com.example.coloringproject.utils.LoadResult.Error -> {
                    Log.d(TAG, "无进度数据: $projectName - ${result.message}")

                    // 列出保存目录下的所有文件，帮助调试
                    val saveDir = java.io.File(filesDir, "coloring_saves")
                    if (saveDir.exists()) {
                        val files = saveDir.listFiles()
                        Log.d(TAG, "coloring_saves目录下的文件: ${files?.map { it.name }}")
                    } else {
                        Log.d(TAG, "coloring_saves目录不存在")
                    }
                }
            }

        } catch (e: Exception) {
            Log.e(TAG, "同步恢复进度失败: $projectName", e)
        }
    }



    /**
     * 尝试恢复项目进度 - 异步版本
     */
    private fun tryRestoreProgress(projectName: String) {
        lifecycleScope.launch {
            try {
                restoreProgressSync(projectName)
            } catch (e: Exception) {
                Log.e(TAG, "异步恢复进度失败: $projectName", e)
            }
        }
    }
}
