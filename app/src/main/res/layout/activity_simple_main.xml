<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background_light"
    android:fitsSystemWindows="true"
    tools:context=".SimpleMainActivityRefactored">

    <!-- 沉浸式顶部栏 -->
    <LinearLayout
        android:id="@+id/topStatusBar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:paddingHorizontal="20dp"
        android:paddingVertical="12dp"
        android:gravity="center_vertical"
        android:background="@android:color/transparent"
        app:layout_constraintTop_toTopOf="parent">

        <!-- 返回按钮 -->
        <FrameLayout
            android:layout_width="44dp"
            android:layout_height="44dp">

            <ImageButton
                android:id="@+id/btnBack"
                android:layout_width="44dp"
                android:layout_height="44dp"
                android:background="@drawable/circle_button_background"
                android:src="@drawable/ic_arrow_back"
                android:contentDescription="返回"
                android:tint="@color/text_primary"
                android:elevation="2dp" />
        </FrameLayout>

        <!-- 中间空白区域 -->
        <View
            android:layout_width="0dp"
            android:layout_height="1dp"
            android:layout_weight="1" />

        <!-- 右侧按钮组 -->
        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical">

            <!-- 商店按钮 -->
            <FrameLayout
                android:layout_width="44dp"
                android:layout_height="44dp"
                android:layout_marginEnd="12dp">

                <ImageButton
                    android:id="@+id/btnShop"
                    android:layout_width="44dp"
                    android:layout_height="44dp"
                    android:background="@drawable/circle_button_background"
                    android:src="@drawable/ic_shop"
                    android:contentDescription="商店"
                    android:tint="@color/accent_color"
                    android:elevation="2dp" />
            </FrameLayout>

            <!-- 涂色提醒按钮 -->
            <FrameLayout
                android:layout_width="44dp"
                android:layout_height="44dp">

                <ImageButton
                    android:id="@+id/btnHint"
                    android:layout_width="44dp"
                    android:layout_height="44dp"
                    android:background="@drawable/circle_button_background"
                    android:src="@drawable/ic_lightbulb"
                    android:contentDescription="涂色提醒"
                    android:tint="@color/warning_color"
                    android:elevation="2dp" />

                <!-- 提醒次数红色圆圈 -->
                <TextView
                    android:id="@+id/tvHintCount"
                    android:layout_width="20dp"
                    android:layout_height="20dp"
                    android:layout_gravity="top|end"
                    android:layout_marginTop="2dp"
                    android:layout_marginEnd="2dp"
                    android:background="@drawable/red_circle_badge"
                    android:text="3"
                    android:textColor="@android:color/white"
                    android:textSize="10sp"
                    android:textStyle="bold"
                    android:gravity="center"
                    android:elevation="3dp" />
            </FrameLayout>

        </LinearLayout>

    </LinearLayout>

    <!-- 沉浸式绘制区域 -->
    <FrameLayout
        android:id="@+id/drawingArea"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintTop_toBottomOf="@id/topStatusBar"
        app:layout_constraintBottom_toTopOf="@id/progressContainer"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <!-- 填色视图 -->
        <com.example.coloringproject.view.ColoringView
            android:id="@+id/coloringView"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@android:color/white" />

        <!-- 加载指示器 -->
        <ProgressBar
            android:id="@+id/loadingIndicator"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:visibility="gone" />

        <!-- 状态文本 -->
        <TextView
            android:id="@+id/tvStatus"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:text="请选择一个填色项目"
            android:textSize="16sp"
            android:textColor="@color/text_secondary"
            android:visibility="visible" />

    </FrameLayout>

    <!-- 浮动缩放控制 -->
    <LinearLayout
        android:id="@+id/zoomControls"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:layout_marginEnd="20dp"
        android:layout_marginBottom="20dp"
        android:background="@drawable/floating_panel_background"
        android:padding="8dp"
        android:elevation="6dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toTopOf="@id/progressContainer">

        <ImageButton
            android:id="@+id/btnZoomIn"
            android:layout_width="36dp"
            android:layout_height="36dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:src="@drawable/ic_zoom_in"
            android:contentDescription="放大"
            android:visibility="gone"
            android:tint="@color/text_primary" />

        <ImageButton
            android:id="@+id/btnZoomOut"
            android:layout_width="36dp"
            android:layout_height="36dp"
            android:layout_marginTop="4dp"
            android:visibility="gone"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:src="@drawable/ic_zoom_out"
            android:contentDescription="缩小"
            android:tint="@color/text_primary" />

        <ImageButton
            android:id="@+id/btnZoomFit"
            android:layout_width="36dp"
            android:layout_height="36dp"
            android:layout_marginTop="4dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:src="@drawable/ic_fit_screen"
            android:contentDescription="适应屏幕"
            android:tint="@color/text_primary" />

    </LinearLayout>

    <!-- 进度条容器 -->
    <LinearLayout
        android:id="@+id/progressContainer"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:background="@android:color/transparent"
        android:paddingHorizontal="20dp"
        android:paddingVertical="12dp"
        app:layout_constraintBottom_toTopOf="@id/colorPalette">


        <!-- 进度文本 -->
        <TextView
            android:id="@+id/tvProgress"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginTop="4dp"
            android:text="0 / 0"
            android:visibility="gone"
            android:textSize="12sp"
            android:textColor="@color/text_secondary"
            android:textStyle="bold" />

        <!-- 当前颜色信息 -->
        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginTop="8dp"
            android:visibility="gone"
            android:orientation="horizontal"
            android:gravity="center_vertical">

            <!-- 当前颜色圆圈 -->
            <View
                android:id="@+id/currentColorView"
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:layout_marginEnd="8dp"
                android:background="@drawable/circle_button_background" />

            <!-- 当前颜色名称和进度 -->
            <TextView
                android:id="@+id/tvCurrentColorName"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="选择颜色"
                android:textSize="14sp"
                android:textColor="@color/text_primary"
                android:textStyle="bold" />

        </LinearLayout>

        <!-- 长进度条 -->
        <ProgressBar
            android:id="@+id/progressBar"
            style="?android:attr/progressBarStyleHorizontal"
            android:layout_width="match_parent"
            android:layout_height="8dp"
            android:max="100"
            android:progress="0"
            android:progressDrawable="@drawable/custom_progress_bar"
            android:background="@drawable/progress_background" />

    </LinearLayout>

    <!-- 底部颜色选择区域 -->
    <LinearLayout
        android:id="@+id/colorPalette"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:background="@color/card_background"
        android:elevation="8dp"
        android:paddingVertical="20dp"
        app:layout_constraintBottom_toBottomOf="parent">

        <!-- 颜色选择器 -->
        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recyclerViewColors"
            android:layout_width="match_parent"
            android:layout_height="80dp"
            android:paddingHorizontal="20dp"
            android:clipToPadding="false" />

    </LinearLayout>

    <!-- 隐藏的测试按钮组 -->
    <LinearLayout
        android:id="@+id/testButtonsGroup"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:layout_margin="16dp"
        android:visibility="gone"
        android:background="@drawable/floating_panel_background"
        android:padding="8dp"
        android:elevation="6dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintBottom_toTopOf="@id/progressContainer">

        <Button
            android:id="@+id/btnToggleHints"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="定位"
            android:textSize="12sp"
            style="@style/Widget.Material3.Button.OutlinedButton"
            android:layout_marginBottom="4dp" />

        <Button
            android:id="@+id/btnAutoDemo"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="演示"
            android:textSize="12sp"
            style="@style/Widget.Material3.Button.OutlinedButton"
            android:layout_marginBottom="4dp" />

        <Button
            android:id="@+id/btnNextColor"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="下一个"
            android:textSize="12sp"
            style="@style/Widget.Material3.Button.OutlinedButton" />

    </LinearLayout>



</androidx.constraintlayout.widget.ConstraintLayout>
